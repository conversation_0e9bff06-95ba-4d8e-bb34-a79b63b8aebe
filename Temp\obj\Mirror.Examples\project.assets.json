{"version": 3, "targets": {".NETStandard,Version=v2.1": {"kcp2k/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Mirror": "1.0.0"}, "compile": {"bin/placeholder/kcp2k.dll": {}}, "runtime": {"bin/placeholder/kcp2k.dll": {}}}, "Mirror/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Mirror.CompilerSymbols": "1.0.0"}, "compile": {"bin/placeholder/Mirror.dll": {}}, "runtime": {"bin/placeholder/Mirror.dll": {}}}, "Mirror.CompilerSymbols/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/Mirror.CompilerSymbols.dll": {}}, "runtime": {"bin/placeholder/Mirror.CompilerSymbols.dll": {}}}, "Mirror.Components/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Mirror": "1.0.0"}, "compile": {"bin/placeholder/Mirror.Components.dll": {}}, "runtime": {"bin/placeholder/Mirror.Components.dll": {}}}, "Mirror.Transports/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Mirror": "1.0.0", "SimpleWebTransport": "1.0.0", "Telepathy": "1.0.0", "kcp2k": "1.0.0"}, "compile": {"bin/placeholder/Mirror.Transports.dll": {}}, "runtime": {"bin/placeholder/Mirror.Transports.dll": {}}}, "SimpleWebTransport/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/SimpleWebTransport.dll": {}}, "runtime": {"bin/placeholder/SimpleWebTransport.dll": {}}}, "Telepathy/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/Telepathy.dll": {}}, "runtime": {"bin/placeholder/Telepathy.dll": {}}}}}, "libraries": {"kcp2k/1.0.0": {"type": "project", "path": "kcp2k.csproj", "msbuildProject": "kcp2k.csproj"}, "Mirror/1.0.0": {"type": "project", "path": "Mirror.csproj", "msbuildProject": "Mirror.csproj"}, "Mirror.CompilerSymbols/1.0.0": {"type": "project", "path": "Mirror.CompilerSymbols.csproj", "msbuildProject": "Mirror.CompilerSymbols.csproj"}, "Mirror.Components/1.0.0": {"type": "project", "path": "Mirror.Components.csproj", "msbuildProject": "Mirror.Components.csproj"}, "Mirror.Transports/1.0.0": {"type": "project", "path": "Mirror.Transports.csproj", "msbuildProject": "Mirror.Transports.csproj"}, "SimpleWebTransport/1.0.0": {"type": "project", "path": "SimpleWebTransport.csproj", "msbuildProject": "SimpleWebTransport.csproj"}, "Telepathy/1.0.0": {"type": "project", "path": "Telepathy.csproj", "msbuildProject": "Telepathy.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["Mirror >= 1.0.0", "Mirror.Components >= 1.0.0", "Mirror.Transports >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "c:\\Users\\<USER>\\ONU\\Mirror.Examples.csproj", "projectName": "Mirror.Examples", "projectPath": "c:\\Users\\<USER>\\ONU\\Mirror.Examples.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "c:\\Users\\<USER>\\ONU\\Temp\\obj\\Mirror.Examples\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"c:\\Users\\<USER>\\ONU\\Mirror.Components.csproj": {"projectPath": "c:\\Users\\<USER>\\ONU\\Mirror.Components.csproj"}, "c:\\Users\\<USER>\\ONU\\Mirror.csproj": {"projectPath": "c:\\Users\\<USER>\\ONU\\Mirror.csproj"}, "c:\\Users\\<USER>\\ONU\\Mirror.Transports.csproj": {"projectPath": "c:\\Users\\<USER>\\ONU\\Mirror.Transports.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.205\\RuntimeIdentifierGraph.json"}}}}