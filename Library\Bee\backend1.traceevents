{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1754507489772282, "dur":2278, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754507489774572, "dur":1070, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754507489775781, "dur":114, "ph":"X", "name": "Tundra",  "args": { "detail":"PrepareNodes" }}
,{ "pid":12345, "tid":0, "ts":1754507489775895, "dur":430, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754507489776597, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_0B408E8BC3EE7994.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1754507489776750, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_DD8A67269492BAE1.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1754507489779414, "dur":2886, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Common.ref.dll_EE36537354EA42C8.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1754507489782882, "dur":77, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Notifications.iOS.ref.dll_F8131B77D746DCAF.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1754507489783384, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.Editor.ref.dll_7989A1C8D57EF9BF.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1754507489784470, "dur":88, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.PlayMode.Configurations.Editor.ref.dll_825D711653F5CC19.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1754507489785059, "dur":81, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":0, "ts":1754507489785746, "dur":83, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":0, "ts":1754507489787405, "dur":1628, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":0, "ts":1754507489789056, "dur":209, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp2" }}
,{ "pid":12345, "tid":0, "ts":1754507489789803, "dur":100, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.Updater.ref.dll_34B8D5B7DED6E76D.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1754507489790404, "dur":96, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mobile.AndroidLogcat.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1754507489791117, "dur":123, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":0, "ts":1754507489791752, "dur":103, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp" }}
,{ "pid":12345, "tid":0, "ts":1754507489792158, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.pdb" }}
,{ "pid":12345, "tid":0, "ts":1754507489793561, "dur":115, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/Telepathy.pdb" }}
,{ "pid":12345, "tid":0, "ts":1754507489797166, "dur":55, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/14402696373757716438.rsp" }}
,{ "pid":12345, "tid":0, "ts":1754507489798842, "dur":140, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/8883882501886164173.rsp" }}
,{ "pid":12345, "tid":0, "ts":1754507489801321, "dur":90, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/kcp2k.pdb" }}
,{ "pid":12345, "tid":0, "ts":1754507489806208, "dur":103, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp" }}
,{ "pid":12345, "tid":0, "ts":1754507489776344, "dur":30355, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754507489806721, "dur":1701512, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754507491508265, "dur":441, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754507491508706, "dur":89, "ph":"X", "name": "ThreadStateDestroy",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754507491508849, "dur":82, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754507491508933, "dur":77, "ph":"X", "name": "ThreadStateDestroy",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754507491509258, "dur":59, "ph":"X", "name": "ThreadStateDestroy",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754507491509392, "dur":583, "ph":"X", "name": "BuildQueueDestroyTail",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754507491510072, "dur":7518, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1754507489776669, "dur":30159, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489806833, "dur":453, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_B67EF2BB05ABACA5.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1754507489807287, "dur":529, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489807818, "dur":445, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_BB19FC7FFBEB112B.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1754507489808263, "dur":936, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489809199, "dur":79, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_BB19FC7FFBEB112B.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1754507489809339, "dur":76, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_8BA1F79F85B3575D.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1754507489809415, "dur":127, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489809549, "dur":55, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_200C40F278319369.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1754507489809605, "dur":607, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489810310, "dur":373, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mirror.CodeGen.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":1, "ts":1754507489810729, "dur":626, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":1, "ts":1754507489811358, "dur":309, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mirror.CodeGen.dll.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1754507489811668, "dur":128, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489811802, "dur":102, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489811905, "dur":1096, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Telepathy.rsp2" }}
,{ "pid":12345, "tid":1, "ts":1754507489813003, "dur":479, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489813692, "dur":648, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Unified.rsp" }}
,{ "pid":12345, "tid":1, "ts":1754507489815048, "dur":66, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/2400031028012695012.rsp" }}
,{ "pid":12345, "tid":1, "ts":1754507489815272, "dur":1530, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/10831531723267835799.rsp" }}
,{ "pid":12345, "tid":1, "ts":1754507489816804, "dur":721, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489817525, "dur":655, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489818181, "dur":753, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489818935, "dur":1421, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489820696, "dur":651, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Window\\Modes\\TimelineActiveMode.cs" }}
,{ "pid":12345, "tid":1, "ts":1754507489820357, "dur":1275, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489821632, "dur":612, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489822245, "dur":636, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489822881, "dur":626, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489823507, "dur":626, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489824133, "dur":641, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489824775, "dur":679, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489825454, "dur":687, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489826142, "dur":672, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489826814, "dur":728, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489827544, "dur":643, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Interface\\Fuzzy\\NullOption.cs" }}
,{ "pid":12345, "tid":1, "ts":1754507489828492, "dur":637, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Interface\\Fuzzy\\FuzzyOptionAttribute.cs" }}
,{ "pid":12345, "tid":1, "ts":1754507489827543, "dur":1973, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489829517, "dur":715, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489830233, "dur":634, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489830868, "dur":669, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489831537, "dur":716, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489832254, "dur":628, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489832882, "dur":710, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489833593, "dur":651, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489834245, "dur":1520, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489835766, "dur":1139, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489836906, "dur":492, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489837399, "dur":563, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489837964, "dur":667, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.dll.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1754507489838632, "dur":444, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489839081, "dur":1086, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.dll (+2 others)" }}
,{ "pid":12345, "tid":1, "ts":1754507489840168, "dur":63, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489840239, "dur":307, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489840550, "dur":234, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1754507489840821, "dur":681, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":1, "ts":1754507489841502, "dur":1000, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489842532, "dur":1098, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mobile.AndroidLogcat.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":1, "ts":1754507489843631, "dur":157, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489843796, "dur":1025, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.dll (+2 others)" }}
,{ "pid":12345, "tid":1, "ts":1754507489844822, "dur":187, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489845018, "dur":216, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489845238, "dur":282, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1754507489845568, "dur":756, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":1, "ts":1754507489846369, "dur":360, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489846730, "dur":282, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489847012, "dur":1031, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489848051, "dur":65, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489848123, "dur":2477, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489850602, "dur":62784, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489913411, "dur":2515, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)" }}
,{ "pid":12345, "tid":1, "ts":1754507489915927, "dur":124, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489916066, "dur":3820, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)" }}
,{ "pid":12345, "tid":1, "ts":1754507489919888, "dur":94, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489919993, "dur":2851, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)" }}
,{ "pid":12345, "tid":1, "ts":1754507489922845, "dur":96, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489922960, "dur":3902, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)" }}
,{ "pid":12345, "tid":1, "ts":1754507489926863, "dur":2825, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489929700, "dur":3844, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.Profiler.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":1, "ts":1754507489933545, "dur":1176, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489934732, "dur":3660, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/EncryptionTransportEditor.dll (+pdb)" }}
,{ "pid":12345, "tid":1, "ts":1754507489938393, "dur":186, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489938911, "dur":148, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489939151, "dur":279, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489939437, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754507489939960, "dur":1568118, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489776585, "dur":30155, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489806758, "dur":324, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489807095, "dur":498, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_82AFBD6736063513.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1754507489807594, "dur":257, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489807858, "dur":71, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_03F87B6D11395095.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1754507489807930, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489808026, "dur":123, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_F904655B297DA2DA.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1754507489808150, "dur":631, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489808832, "dur":59, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489808927, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489809026, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489809134, "dur":426, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489809604, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489809698, "dur":87, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489809797, "dur":114, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_1280E01E43015E29.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1754507489809912, "dur":248, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489810176, "dur":213, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489810597, "dur":177, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":2, "ts":1754507489810782, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489810880, "dur":1154, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.Common.Runtime.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":2, "ts":1754507489812043, "dur":82, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489812157, "dur":50, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.UI.Editor.rsp" }}
,{ "pid":12345, "tid":2, "ts":1754507489812208, "dur":95, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489812308, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489812938, "dur":225, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489813257, "dur":1774, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":2, "ts":1754507489815033, "dur":69, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/9321109326917711913.rsp" }}
,{ "pid":12345, "tid":2, "ts":1754507489815264, "dur":992, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/14814235663552238418.rsp" }}
,{ "pid":12345, "tid":2, "ts":1754507489816257, "dur":993, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489817251, "dur":633, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489817884, "dur":823, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489818708, "dur":716, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489820606, "dur":618, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.ide.visualstudio@198cdf337d13\\Editor\\AsyncOperation.cs" }}
,{ "pid":12345, "tid":2, "ts":1754507489819424, "dur":1801, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489821225, "dur":613, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Manipulators\\Sequence\\TrackZoom.cs" }}
,{ "pid":12345, "tid":2, "ts":1754507489821225, "dur":1507, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489822733, "dur":645, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489823379, "dur":673, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489824053, "dur":672, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489824996, "dur":764, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.universal@39bda2df468a\\Runtime\\2D\\Rendergraph\\DrawRenderer2DPass.cs" }}
,{ "pid":12345, "tid":2, "ts":1754507489826019, "dur":810, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.universal@39bda2df468a\\Runtime\\2D\\Renderer2D.cs" }}
,{ "pid":12345, "tid":2, "ts":1754507489824726, "dur":2266, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489826993, "dur":863, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489827856, "dur":744, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489828601, "dur":664, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489829266, "dur":709, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489829975, "dur":672, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489830647, "dur":680, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489831327, "dur":776, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489832104, "dur":765, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489832869, "dur":809, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489833840, "dur":1058, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.mathematics@8017b507cc74\\Unity.Mathematics\\double4.gen.cs" }}
,{ "pid":12345, "tid":2, "ts":1754507489833678, "dur":1509, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489835517, "dur":633, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.ugui@f3fac7af1578\\Editor\\UGUI\\UI\\SliderEditor.cs" }}
,{ "pid":12345, "tid":2, "ts":1754507489835187, "dur":1687, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489836874, "dur":515, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489837390, "dur":576, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489837968, "dur":376, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.Common.Runtime.dll.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1754507489838345, "dur":314, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489838667, "dur":680, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.Common.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":2, "ts":1754507489839348, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489839431, "dur":211, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489839646, "dur":436, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.VirtualProjects.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1754507489840083, "dur":284, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489840375, "dur":1134, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.VirtualProjects.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":2, "ts":1754507489841509, "dur":758, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489842334, "dur":398, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.Workflow.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1754507489842733, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489842810, "dur":707, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.Workflow.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":2, "ts":1754507489843518, "dur":67, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489843615, "dur":215, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.dll.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1754507489843856, "dur":583, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.dll (+2 others)" }}
,{ "pid":12345, "tid":2, "ts":1754507489844439, "dur":65, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489844532, "dur":652, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Mirror.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":2, "ts":1754507489845185, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489845254, "dur":186, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1754507489845467, "dur":629, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)" }}
,{ "pid":12345, "tid":2, "ts":1754507489846096, "dur":316, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489846414, "dur":284, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489846699, "dur":121, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1754507489846844, "dur":764, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":2, "ts":1754507489847609, "dur":299, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489847942, "dur":159, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1754507489848124, "dur":617, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":2, "ts":1754507489848803, "dur":96, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1754507489848916, "dur":532, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":2, "ts":1754507489849449, "dur":98, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489849600, "dur":993, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489850595, "dur":62830, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489913427, "dur":5281, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)" }}
,{ "pid":12345, "tid":2, "ts":1754507489918710, "dur":650, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489919374, "dur":3363, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)" }}
,{ "pid":12345, "tid":2, "ts":1754507489922738, "dur":119, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489922865, "dur":3692, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Profiling.Core.dll (+pdb)" }}
,{ "pid":12345, "tid":2, "ts":1754507489926558, "dur":1667, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489928237, "dur":3791, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)" }}
,{ "pid":12345, "tid":2, "ts":1754507489932029, "dur":469, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489932534, "dur":3589, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":2, "ts":1754507489936124, "dur":111, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489936249, "dur":3733, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.PlayMode.Configurations.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":2, "ts":1754507489939983, "dur":81, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754507489940116, "dur":1568163, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489776817, "dur":30181, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489807006, "dur":391, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_D1C7B996A6C45BF4.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1754507489807397, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489807471, "dur":192, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_2A749ACEEBE3A48E.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1754507489807663, "dur":489, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489808200, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489808291, "dur":85, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489808382, "dur":85, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_96C3CEE09404C3EA.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1754507489808468, "dur":704, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489809287, "dur":60, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_6F907A205E01B872.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1754507489809348, "dur":232, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489809596, "dur":141, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489809738, "dur":477, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":3, "ts":1754507489810917, "dur":2075, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":3, "ts":1754507489812995, "dur":301, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489813310, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489813389, "dur":94, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489813515, "dur":101, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.rsp" }}
,{ "pid":12345, "tid":3, "ts":1754507489813618, "dur":98, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489813717, "dur":1232, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.rsp" }}
,{ "pid":12345, "tid":3, "ts":1754507489814985, "dur":52, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/2240406767038398906.rsp" }}
,{ "pid":12345, "tid":3, "ts":1754507489815038, "dur":136, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489815180, "dur":75, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/10292501669419677951.rsp" }}
,{ "pid":12345, "tid":3, "ts":1754507489815287, "dur":3764, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/10292501669419677951.rsp" }}
,{ "pid":12345, "tid":3, "ts":1754507489819052, "dur":565, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489819617, "dur":493, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489820110, "dur":500, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489820703, "dur":626, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Signals\\TreeView\\SignalReceiverItem.cs" }}
,{ "pid":12345, "tid":3, "ts":1754507489820610, "dur":1188, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489821798, "dur":626, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489822424, "dur":641, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489823066, "dur":661, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489823728, "dur":645, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489824373, "dur":817, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489825191, "dur":677, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489825869, "dur":656, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489826525, "dur":669, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489827194, "dur":711, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489827905, "dur":678, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489828583, "dur":654, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489829238, "dur":709, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489829947, "dur":801, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489830748, "dur":665, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489831413, "dur":917, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489832330, "dur":658, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489832988, "dur":723, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489833711, "dur":647, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489834516, "dur":836, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\PlasticApp.cs" }}
,{ "pid":12345, "tid":3, "ts":1754507489834358, "dur":2595, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489836954, "dur":437, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489837391, "dur":662, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489838054, "dur":590, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.iOS.dll.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1754507489838691, "dur":863, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.iOS.dll (+2 others)" }}
,{ "pid":12345, "tid":3, "ts":1754507489839555, "dur":117, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489839680, "dur":304, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PackageValidationSuite.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1754507489840006, "dur":656, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PackageValidationSuite.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":3, "ts":1754507489840662, "dur":153, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489840822, "dur":645, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/SimpleWebTransport.dll (+2 others)" }}
,{ "pid":12345, "tid":3, "ts":1754507489841468, "dur":303, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489841775, "dur":1216, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Mirror.Transports.dll (+2 others)" }}
,{ "pid":12345, "tid":3, "ts":1754507489842991, "dur":749, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489843744, "dur":249, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1754507489844022, "dur":537, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":3, "ts":1754507489844559, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489844622, "dur":125, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489844835, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489845015, "dur":174, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1754507489845213, "dur":227, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.WorkflowUI.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1754507489845465, "dur":780, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.WorkflowUI.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":3, "ts":1754507489846246, "dur":119, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489846372, "dur":325, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489846698, "dur":189, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1754507489847045, "dur":654, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)" }}
,{ "pid":12345, "tid":3, "ts":1754507489847700, "dur":147, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489847883, "dur":2856, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489850740, "dur":62662, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489913407, "dur":3846, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)" }}
,{ "pid":12345, "tid":3, "ts":1754507489917261, "dur":99, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489917373, "dur":3023, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)" }}
,{ "pid":12345, "tid":3, "ts":1754507489920397, "dur":1162, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489921568, "dur":4514, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)" }}
,{ "pid":12345, "tid":3, "ts":1754507489926083, "dur":1376, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489927488, "dur":3757, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":3, "ts":1754507489931246, "dur":668, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489931927, "dur":4351, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":3, "ts":1754507489936279, "dur":93, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489936473, "dur":117, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489936602, "dur":714, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489937379, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489937440, "dur":198, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489937653, "dur":722, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489938407, "dur":94, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489938547, "dur":269, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489938824, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489938916, "dur":56, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489939160, "dur":356, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489939581, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489939638, "dur":75, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489939755, "dur":53, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/Edgegap.pdb" }}
,{ "pid":12345, "tid":3, "ts":1754507489939985, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754507489940045, "dur":1568250, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489776626, "dur":30170, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489806815, "dur":496, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_08B2EE5ACE61D9A1.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1754507489807312, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489807427, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489807534, "dur":371, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489807910, "dur":107, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_22A35CACB552B292.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1754507489808018, "dur":1141, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489809294, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489809502, "dur":259, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489809799, "dur":73, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_7F371AF1A9E7F3FC.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1754507489809873, "dur":85, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489809966, "dur":52, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp" }}
,{ "pid":12345, "tid":4, "ts":1754507489810019, "dur":118, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489810142, "dur":236, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489810386, "dur":96, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_EC1F0C20321316E3.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1754507489810482, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489810736, "dur":786, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":4, "ts":1754507489811888, "dur":1080, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Android.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":4, "ts":1754507489812970, "dur":271, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489813277, "dur":126, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp" }}
,{ "pid":12345, "tid":4, "ts":1754507489813684, "dur":393, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp" }}
,{ "pid":12345, "tid":4, "ts":1754507489814117, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489814284, "dur":101, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489814843, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489814938, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489815295, "dur":3834, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/13938709970307456411.rsp" }}
,{ "pid":12345, "tid":4, "ts":1754507489819131, "dur":1111, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489820688, "dur":1114, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.mobile.android-logcat@e05958e55753\\Editor\\AndroidLogcatAutoScroll.cs" }}
,{ "pid":12345, "tid":4, "ts":1754507489820243, "dur":1791, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489822034, "dur":645, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489822800, "dur":559, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.shadergraph@5c8f96f445ce\\Editor\\Serialization\\SerializationExtensions.cs" }}
,{ "pid":12345, "tid":4, "ts":1754507489822680, "dur":1247, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489823927, "dur":650, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489824578, "dur":736, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489825314, "dur":678, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489825992, "dur":869, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489826862, "dur":720, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489827582, "dur":675, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489828257, "dur":706, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489828964, "dur":708, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489829672, "dur":728, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489830401, "dur":676, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489831078, "dur":674, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489831752, "dur":714, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489832467, "dur":755, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489833223, "dur":676, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489833900, "dur":1285, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489835186, "dur":868, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489836055, "dur":641, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489836745, "dur":640, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489837386, "dur":527, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489837914, "dur":236, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Mirror.dll.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1754507489838151, "dur":834, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489838992, "dur":755, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Android.dll (+2 others)" }}
,{ "pid":12345, "tid":4, "ts":1754507489839747, "dur":658, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489840409, "dur":209, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/kcp2k.dll.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1754507489840650, "dur":648, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/kcp2k.dll (+2 others)" }}
,{ "pid":12345, "tid":4, "ts":1754507489841299, "dur":371, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489841673, "dur":434, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PackageValidationSuite.Editor.Extension.dll.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1754507489842108, "dur":98, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489842221, "dur":185, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mobile.AndroidLogcat.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1754507489842407, "dur":117, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489842530, "dur":831, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":4, "ts":1754507489843361, "dur":112, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489843477, "dur":197, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1754507489843724, "dur":1005, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":4, "ts":1754507489844729, "dur":2091, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489846961, "dur":155, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1754507489847116, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489847186, "dur":441, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":4, "ts":1754507489847628, "dur":131, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489847788, "dur":99, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1754507489847903, "dur":573, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":4, "ts":1754507489848477, "dur":219, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489848749, "dur":170, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1754507489848924, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489849002, "dur":522, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)" }}
,{ "pid":12345, "tid":4, "ts":1754507489849524, "dur":109, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489849656, "dur":1083, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489850739, "dur":62764, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489913506, "dur":4159, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Mirror.Examples.dll (+pdb)" }}
,{ "pid":12345, "tid":4, "ts":1754507489917667, "dur":107, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489917787, "dur":4248, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)" }}
,{ "pid":12345, "tid":4, "ts":1754507489922036, "dur":1854, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489923900, "dur":4269, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Playmode.WorkflowUI.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":4, "ts":1754507489928170, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489928261, "dur":3583, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)" }}
,{ "pid":12345, "tid":4, "ts":1754507489931845, "dur":663, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489932542, "dur":3402, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)" }}
,{ "pid":12345, "tid":4, "ts":1754507489935945, "dur":90, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489936047, "dur":3512, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":4, "ts":1754507489939560, "dur":87, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489939945, "dur":139, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754507489940112, "dur":1567940, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754507489776576, "dur":30152, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754507489806758, "dur":189, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754507489806970, "dur":617, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_A9644AD1997CC33F.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1754507489807587, "dur":693, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754507489808382, "dur":466, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_B5CC5E98A61A2700.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1754507489808849, "dur":63, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754507489808983, "dur":365, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754507489809469, "dur":156, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754507489809635, "dur":704, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754507489810339, "dur":981, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":5, "ts":1754507489811922, "dur":1114, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp" }}
,{ "pid":12345, "tid":5, "ts":1754507489813038, "dur":691, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754507489813729, "dur":1608, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":5, "ts":1754507489815365, "dur":107, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754507489815477, "dur":50, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/1723042715960067801.rsp" }}
,{ "pid":12345, "tid":5, "ts":1754507489815738, "dur":654, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754507489816393, "dur":980, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754507489817374, "dur":651, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754507489818026, "dur":732, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754507489818758, "dur":881, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754507489819639, "dur":673, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754507489820698, "dur":4564, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Window\\TimelineWindow_Duration.cs" }}
,{ "pid":12345, "tid":5, "ts":1754507489820313, "dur":5145, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754507489825458, "dur":466, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754507489825924, "dur":455, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754507489826379, "dur":476, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754507489826856, "dur":486, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754507489827342, "dur":657, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754507489828158, "dur":825, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.ugui@f3fac7af1578\\Runtime\\TMP\\TMP_Asset.cs" }}
,{ "pid":12345, "tid":5, "ts":1754507489829434, "dur":2613, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.ugui@f3fac7af1578\\Runtime\\TMP\\FastAction.cs" }}
,{ "pid":12345, "tid":5, "ts":1754507489828000, "dur":4118, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754507489832118, "dur":668, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754507489832787, "dur":1071, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754507489833859, "dur":1343, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754507489835202, "dur":862, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754507489836209, "dur":67, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754507489836392, "dur":330, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754507489836735, "dur":636, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754507489837371, "dur":520, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754507489837892, "dur":143, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Mirror.CompilerSymbols.dll.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1754507489838064, "dur":866, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Mirror.CompilerSymbols.dll (+2 others)" }}
,{ "pid":12345, "tid":5, "ts":1754507489838930, "dur":155, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754507489839097, "dur":1264, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Mirror.dll (+2 others)" }}
,{ "pid":12345, "tid":5, "ts":1754507489840362, "dur":214, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754507489840588, "dur":682, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mirror.CodeGen.dll (+2 others)" }}
,{ "pid":12345, "tid":5, "ts":1754507489841271, "dur":174, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754507489841456, "dur":195, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1754507489841688, "dur":316, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1754507489842004, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754507489842062, "dur":249, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1754507489842311, "dur":279, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754507489842597, "dur":657, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)" }}
,{ "pid":12345, "tid":5, "ts":1754507489843255, "dur":119, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754507489843378, "dur":560, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)" }}
,{ "pid":12345, "tid":5, "ts":1754507489843938, "dur":1398, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754507489845431, "dur":268, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754507489845788, "dur":948, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754507489846737, "dur":312, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754507489847050, "dur":3477, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754507489850529, "dur":158, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1754507489850704, "dur":62715, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754507489913427, "dur":4031, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Mirror.Transports.dll (+pdb)" }}
,{ "pid":12345, "tid":5, "ts":1754507489917459, "dur":918, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754507489918390, "dur":3888, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Mirror.Authenticators.dll (+pdb)" }}
,{ "pid":12345, "tid":5, "ts":1754507489922279, "dur":193, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754507489922541, "dur":4872, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)" }}
,{ "pid":12345, "tid":5, "ts":1754507489927415, "dur":102, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754507489927523, "dur":3856, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.dll (+pdb)" }}
,{ "pid":12345, "tid":5, "ts":1754507489931380, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754507489931470, "dur":4662, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.Simulator.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":5, "ts":1754507489936134, "dur":103, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754507489936255, "dur":3508, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Playmode.Workflow.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":5, "ts":1754507489939764, "dur":86, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754507489939951, "dur":188334, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754507490128352, "dur":350815, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":5, "ts":1754507490128297, "dur":353205, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)" }}
,{ "pid":12345, "tid":5, "ts":1754507490483191, "dur":346, "ph":"X", "name": "StoreTimestampsOfNonGeneratedInputFiles",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754507490484128, "dur":285416, "ph":"X", "name": "ILPostProcess",  "args": { "detail":"Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)" }}
,{ "pid":12345, "tid":5, "ts":1754507490785166, "dur":436926, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":5, "ts":1754507490785150, "dur":436947, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":5, "ts":1754507491228261, "dur":119, "ph":"X", "name": "EmitNodeFinish",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754507491222183, "dur":7085, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/ScriptAssemblies/Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":5, "ts":1754507491229288, "dur":278761, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489776916, "dur":30145, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489807064, "dur":475, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_8F31C36E8B05B73D.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1754507489807539, "dur":694, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489808256, "dur":1101, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489809378, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489809448, "dur":245, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489809693, "dur":303, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_1548EEFA48845E9E.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1754507489810316, "dur":623, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_3453041B594DA597.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1754507489811069, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489811128, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489811259, "dur":1789, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Editor.Bridge.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":6, "ts":1754507489813050, "dur":724, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489813986, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489814286, "dur":234, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489814527, "dur":92, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/18218310762646611085.rsp" }}
,{ "pid":12345, "tid":6, "ts":1754507489814705, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489814780, "dur":70, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/15085861467720516389.rsp" }}
,{ "pid":12345, "tid":6, "ts":1754507489815281, "dur":2895, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/6886376194011666012.rsp" }}
,{ "pid":12345, "tid":6, "ts":1754507489818177, "dur":538, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489818716, "dur":753, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489819469, "dur":702, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489820684, "dur":597, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.mobile.android-logcat@e05958e55753\\Editor\\AndroidLogcatMemoryViewer.cs" }}
,{ "pid":12345, "tid":6, "ts":1754507489820172, "dur":1330, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489821503, "dur":596, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489822099, "dur":689, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489822789, "dur":775, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489823564, "dur":630, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489824195, "dur":639, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489824835, "dur":696, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489825531, "dur":677, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489826209, "dur":829, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489827039, "dur":840, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489827880, "dur":855, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489828735, "dur":679, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489829415, "dur":704, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489830119, "dur":664, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489830783, "dur":678, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489832004, "dur":1028, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Core\\Cloning\\Cloners\\AnimationCurveCloner.cs" }}
,{ "pid":12345, "tid":6, "ts":1754507489831461, "dur":1732, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489833194, "dur":695, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489834899, "dur":701, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\Views\\Locks\\LocksViewMenu.cs" }}
,{ "pid":12345, "tid":6, "ts":1754507489833890, "dur":1710, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489835601, "dur":1306, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489836907, "dur":574, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489837482, "dur":567, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489838051, "dur":883, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1754507489838936, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489839006, "dur":1170, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)" }}
,{ "pid":12345, "tid":6, "ts":1754507489840177, "dur":614, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489840840, "dur":331, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1754507489841206, "dur":1607, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":6, "ts":1754507489842814, "dur":132, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489842969, "dur":154, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489843127, "dur":703, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PackageValidationSuite.Editor.Extension.dll (+2 others)" }}
,{ "pid":12345, "tid":6, "ts":1754507489843830, "dur":101, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489843935, "dur":232, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Mirror.Authenticators.dll.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1754507489844258, "dur":231, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1754507489844490, "dur":438, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489844932, "dur":1628, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":6, "ts":1754507489846622, "dur":121, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489846743, "dur":268, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489847012, "dur":549, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489847563, "dur":188, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Profiler.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1754507489847752, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489847829, "dur":519, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Profiler.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":6, "ts":1754507489848349, "dur":101, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489848460, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489848520, "dur":2147, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489850667, "dur":62749, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489913418, "dur":3850, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)" }}
,{ "pid":12345, "tid":6, "ts":1754507489917269, "dur":2875, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489920154, "dur":3655, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.iOS.dll (+pdb)" }}
,{ "pid":12345, "tid":6, "ts":1754507489923810, "dur":128, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489923948, "dur":4829, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.PlayMode.Editor.Bridge.dll (+pdb)" }}
,{ "pid":12345, "tid":6, "ts":1754507489928778, "dur":939, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489929725, "dur":4884, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":6, "ts":1754507489934610, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489934701, "dur":4510, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.unity.cinemachine.editor.dll (+pdb)" }}
,{ "pid":12345, "tid":6, "ts":1754507489939218, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489939370, "dur":105, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.dll" }}
,{ "pid":12345, "tid":6, "ts":1754507489939476, "dur":60, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Playmode.Common.Editor.pdb" }}
,{ "pid":12345, "tid":6, "ts":1754507489939818, "dur":131, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507489939953, "dur":845244, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754507490785270, "dur":151, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb" }}
,{ "pid":12345, "tid":6, "ts":1754507490785199, "dur":223, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb" }}
,{ "pid":12345, "tid":6, "ts":1754507490785482, "dur":2196, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/ScriptAssemblies/Assembly-CSharp.pdb" }}
,{ "pid":12345, "tid":6, "ts":1754507490787684, "dur":720581, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754507489776611, "dur":30171, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754507489806795, "dur":734, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_5ECB2C4C110CA6E5.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1754507489807530, "dur":173, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754507489807705, "dur":614, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_2BE1F67E8EB9FC15.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1754507489808320, "dur":1481, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754507489809925, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754507489809996, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754507489810068, "dur":104, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_8C9D4507428A9FC0.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1754507489810173, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754507489810304, "dur":60, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":7, "ts":1754507489810457, "dur":195, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754507489810895, "dur":2058, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp" }}
,{ "pid":12345, "tid":7, "ts":1754507489812955, "dur":744, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754507489813699, "dur":839, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp2" }}
,{ "pid":12345, "tid":7, "ts":1754507489814564, "dur":56, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754507489814624, "dur":74, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/4014436084419441659.rsp" }}
,{ "pid":12345, "tid":7, "ts":1754507489814700, "dur":206, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754507489815061, "dur":74, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/3938377011463375229.rsp" }}
,{ "pid":12345, "tid":7, "ts":1754507489815296, "dur":3907, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/3489607026336439425.rsp" }}
,{ "pid":12345, "tid":7, "ts":1754507489819205, "dur":590, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754507489819795, "dur":760, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754507489820693, "dur":4932, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\treeview\\TimelineClipUnion.cs" }}
,{ "pid":12345, "tid":7, "ts":1754507489820556, "dur":5486, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754507489826043, "dur":539, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754507489826582, "dur":687, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754507489827465, "dur":1258, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Plugin\\Changelogs\\LegacyLudiqGraphs\\Changelog_1_0_2.cs" }}
,{ "pid":12345, "tid":7, "ts":1754507489828923, "dur":2392, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Plugin\\Changelogs\\LegacyLudiqCore\\Changelog_1_4_6.cs" }}
,{ "pid":12345, "tid":7, "ts":1754507489827270, "dur":4292, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754507489831562, "dur":801, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754507489832364, "dur":710, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754507489833074, "dur":661, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754507489833735, "dur":1195, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754507489834931, "dur":1091, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754507489836023, "dur":796, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754507489836820, "dur":502, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754507489837324, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754507489837386, "dur":503, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754507489837891, "dur":141, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1754507489838067, "dur":980, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)" }}
,{ "pid":12345, "tid":7, "ts":1754507489839048, "dur":1078, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754507489840160, "dur":246, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754507489840422, "dur":363, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1754507489840786, "dur":121, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754507489840910, "dur":1491, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)" }}
,{ "pid":12345, "tid":7, "ts":1754507489842402, "dur":253, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754507489842674, "dur":771, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754507489843449, "dur":157, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1754507489843632, "dur":595, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)" }}
,{ "pid":12345, "tid":7, "ts":1754507489844227, "dur":786, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754507489845022, "dur":978, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput" }}
,{ "pid":12345, "tid":7, "ts":1754507489846001, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754507489846060, "dur":270, "ph":"X", "name": "StoreTimestampsOfNonGeneratedInputFiles",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754507489847002, "dur":59670, "ph":"X", "name": "ILPP-Configuration",  "args": { "detail":"Library/ilpp-configuration.nevergeneratedoutput" }}
,{ "pid":12345, "tid":7, "ts":1754507489913390, "dur":2822, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)" }}
,{ "pid":12345, "tid":7, "ts":1754507489916213, "dur":122, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754507489916341, "dur":3229, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)" }}
,{ "pid":12345, "tid":7, "ts":1754507489919572, "dur":1140, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754507489920719, "dur":3013, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Mirror.dll (+pdb)" }}
,{ "pid":12345, "tid":7, "ts":1754507489923734, "dur":87, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754507489923834, "dur":3782, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Edgegap.dll (+pdb)" }}
,{ "pid":12345, "tid":7, "ts":1754507489927617, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754507489927712, "dur":3733, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":7, "ts":1754507489931446, "dur":685, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754507489932141, "dur":2989, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":7, "ts":1754507489935132, "dur":108, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754507489935254, "dur":3702, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Playmode.Common.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":7, "ts":1754507489938958, "dur":87, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754507489939045, "dur":400, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Playmode.Common.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":7, "ts":1754507489939452, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754507489939560, "dur":163, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754507489939732, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754507489939810, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754507489939881, "dur":120, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754507489940006, "dur":1568115, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489776789, "dur":30184, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489806977, "dur":347, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_28478B3677CA5D72.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1754507489807327, "dur":293, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489807642, "dur":480, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489808219, "dur":1277, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_13781B3B44A52232.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1754507489809552, "dur":189, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489809966, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489810053, "dur":56, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489810309, "dur":441, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp" }}
,{ "pid":12345, "tid":8, "ts":1754507489810884, "dur":130, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.Common.Editor.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":8, "ts":1754507489811617, "dur":77, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp" }}
,{ "pid":12345, "tid":8, "ts":1754507489811830, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489812556, "dur":112, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489812673, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489812755, "dur":544, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489813675, "dur":76, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.rsp" }}
,{ "pid":12345, "tid":8, "ts":1754507489814345, "dur":158, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489815036, "dur":57, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/15312589170787849123.rsp" }}
,{ "pid":12345, "tid":8, "ts":1754507489815245, "dur":68, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/10131411833777065430.rsp" }}
,{ "pid":12345, "tid":8, "ts":1754507489815744, "dur":573, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489816317, "dur":723, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489817040, "dur":618, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489817659, "dur":608, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489818267, "dur":986, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489819253, "dur":479, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489819732, "dur":716, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489820697, "dur":590, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Undo\\UndoExtensions.cs" }}
,{ "pid":12345, "tid":8, "ts":1754507489820448, "dur":979, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489821428, "dur":283, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489821711, "dur":404, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489822115, "dur":319, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489822434, "dur":320, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489822754, "dur":374, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489823128, "dur":319, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489823448, "dur":324, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489823772, "dur":323, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489824095, "dur":342, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489824437, "dur":408, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489824845, "dur":509, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489825354, "dur":461, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489825815, "dur":482, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489826297, "dur":458, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489826755, "dur":473, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489827229, "dur":659, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489827888, "dur":469, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489828399, "dur":513, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489828912, "dur":493, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489829406, "dur":501, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489829907, "dur":917, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489830824, "dur":521, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489831345, "dur":513, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489831858, "dur":465, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489832324, "dur":495, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489832820, "dur":513, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489833334, "dur":573, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489833908, "dur":940, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489834848, "dur":795, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489836082, "dur":660, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@388540dd9ce6\\UnityEditor.TestRunner\\TestRun\\Tasks\\Scene\\RemoveAdditionalUntitledSceneTask.cs" }}
,{ "pid":12345, "tid":8, "ts":1754507489835643, "dur":1099, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489836743, "dur":591, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489837334, "dur":636, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489837971, "dur":361, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1754507489838332, "dur":561, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489838896, "dur":1095, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":8, "ts":1754507489839992, "dur":99, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489840560, "dur":291, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Telepathy.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1754507489840900, "dur":487, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Telepathy.dll (+2 others)" }}
,{ "pid":12345, "tid":8, "ts":1754507489841388, "dur":118, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489841509, "dur":339, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1754507489841849, "dur":664, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489842516, "dur":741, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":8, "ts":1754507489843258, "dur":85, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489843347, "dur":776, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":8, "ts":1754507489844123, "dur":1547, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489845792, "dur":948, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489846740, "dur":275, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489847015, "dur":3528, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489850543, "dur":62853, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489913398, "dur":3764, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)" }}
,{ "pid":12345, "tid":8, "ts":1754507489917163, "dur":118, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489917289, "dur":4229, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SimpleWebTransport.dll (+pdb)" }}
,{ "pid":12345, "tid":8, "ts":1754507489921519, "dur":483, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489922011, "dur":4466, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Playmode.dll (+pdb)" }}
,{ "pid":12345, "tid":8, "ts":1754507489926478, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489926542, "dur":2988, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)" }}
,{ "pid":12345, "tid":8, "ts":1754507489929531, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489929609, "dur":2817, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.Simulator.Extension.dll (+pdb)" }}
,{ "pid":12345, "tid":8, "ts":1754507489932427, "dur":351, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489932797, "dur":2777, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.UI.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":8, "ts":1754507489935580, "dur":492, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489936080, "dur":2797, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Mirror.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":8, "ts":1754507489938929, "dur":504, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489939497, "dur":168, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489939942, "dur":52, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754507489940020, "dur":1568091, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489776719, "dur":30127, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489806850, "dur":478, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_CBC203599E566618.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1754507489807328, "dur":200, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489807549, "dur":133, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489807685, "dur":603, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_95189FE831A76367.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1754507489808289, "dur":224, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489808514, "dur":515, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_95189FE831A76367.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1754507489809109, "dur":65, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489809475, "dur":54, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_5E09CE17EB61387A.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1754507489809529, "dur":239, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489810278, "dur":220, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489810504, "dur":52, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp" }}
,{ "pid":12345, "tid":9, "ts":1754507489810661, "dur":938, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp2" }}
,{ "pid":12345, "tid":9, "ts":1754507489811791, "dur":65, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489811885, "dur":56, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Configurations.Editor.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":9, "ts":1754507489812394, "dur":154, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489812582, "dur":58, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp2" }}
,{ "pid":12345, "tid":9, "ts":1754507489812751, "dur":82, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489812887, "dur":229, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489813243, "dur":2344, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":9, "ts":1754507489815982, "dur":1090, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.cinemachine@dcc61ebd6655\\Editor\\Utility\\CinemachineSceneTools.cs" }}
,{ "pid":12345, "tid":9, "ts":1754507489817072, "dur":971, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.cinemachine@dcc61ebd6655\\Editor\\Utility\\CinemachineLensPresets.cs" }}
,{ "pid":12345, "tid":9, "ts":1754507489815760, "dur":2401, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489818162, "dur":510, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489818673, "dur":1325, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489819998, "dur":668, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489820689, "dur":555, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Recording\\AnimationTrackRecorder.cs" }}
,{ "pid":12345, "tid":9, "ts":1754507489821244, "dur":4840, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Properties\\AssemblyInfo.cs" }}
,{ "pid":12345, "tid":9, "ts":1754507489820666, "dur":5828, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489826494, "dur":494, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489826988, "dur":438, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489827426, "dur":475, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489827902, "dur":1146, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489829048, "dur":505, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489829553, "dur":489, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489830042, "dur":509, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489830552, "dur":454, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489831006, "dur":472, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489831479, "dur":491, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489831970, "dur":455, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489832425, "dur":488, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489832913, "dur":480, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489833626, "dur":614, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\InputSystem\\Controls\\InputControlLayout.cs" }}
,{ "pid":12345, "tid":9, "ts":1754507489833393, "dur":1081, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489834475, "dur":1171, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489835647, "dur":680, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489836328, "dur":50, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489836378, "dur":213, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489836613, "dur":149, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489836762, "dur":558, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489837324, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489837392, "dur":653, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489838047, "dur":260, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1754507489838308, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489838367, "dur":1307, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)" }}
,{ "pid":12345, "tid":9, "ts":1754507489839675, "dur":642, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489840329, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489840385, "dur":225, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1754507489840649, "dur":694, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":9, "ts":1754507489841344, "dur":398, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489841786, "dur":202, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1754507489842028, "dur":213, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1754507489842264, "dur":379, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1754507489842643, "dur":930, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489843577, "dur":201, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1754507489843800, "dur":1089, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":9, "ts":1754507489844889, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489844975, "dur":348, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489845325, "dur":650, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)" }}
,{ "pid":12345, "tid":9, "ts":1754507489845975, "dur":284, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489846261, "dur":439, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489846700, "dur":322, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489847023, "dur":3510, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489850534, "dur":62866, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489913401, "dur":2784, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)" }}
,{ "pid":12345, "tid":9, "ts":1754507489916191, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489916275, "dur":2746, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.Android.dll (+pdb)" }}
,{ "pid":12345, "tid":9, "ts":1754507489919022, "dur":1132, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489920161, "dur":5718, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)" }}
,{ "pid":12345, "tid":9, "ts":1754507489925880, "dur":912, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489926816, "dur":2728, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":9, "ts":1754507489929545, "dur":1559, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489931112, "dur":2786, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)" }}
,{ "pid":12345, "tid":9, "ts":1754507489933899, "dur":2169, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489936075, "dur":3492, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":9, "ts":1754507489939568, "dur":390, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754507489939987, "dur":1568181, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489776767, "dur":30112, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489806882, "dur":404, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_FEB99ECBF3C51413.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1754507489807288, "dur":127, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489807423, "dur":52, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_B0FA0C1272387829.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1754507489807476, "dur":153, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489807649, "dur":474, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489808258, "dur":252, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489808655, "dur":445, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489809124, "dur":75, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489809199, "dur":56, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_CC1E7410E5880845.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1754507489809673, "dur":82, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489809855, "dur":488, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489810343, "dur":1629, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_570D95476513426D.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1754507489811989, "dur":137, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1754507489812173, "dur":5105, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)" }}
,{ "pid":12345, "tid":10, "ts":1754507489817279, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489817372, "dur":298, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489817670, "dur":313, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489817983, "dur":909, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489818893, "dur":573, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489819466, "dur":482, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489820325, "dur":1396, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.multiplayer.playmode@7f4b34d911b7\\Scenarios\\Editor\\Api\\ScenarioState.cs" }}
,{ "pid":12345, "tid":10, "ts":1754507489819949, "dur":2129, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489822079, "dur":345, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489822424, "dur":333, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489822758, "dur":412, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489823170, "dur":308, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489823479, "dur":325, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489823804, "dur":342, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489824146, "dur":334, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489824480, "dur":356, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489824837, "dur":434, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489825271, "dur":470, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489826185, "dur":1379, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.core@b1fa9fba9fd6\\Editor\\Lighting\\LightAnchorEditorTool.cs" }}
,{ "pid":12345, "tid":10, "ts":1754507489825742, "dur":1822, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489827565, "dur":773, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489828380, "dur":544, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489828925, "dur":506, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489829432, "dur":482, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489829915, "dur":541, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489830491, "dur":629, "ph":"X", "name": "File",  "args": { "detail":"Assets\\Mirror\\Core\\InterestManagement.cs" }}
,{ "pid":12345, "tid":10, "ts":1754507489830456, "dur":1077, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489831534, "dur":512, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489832046, "dur":479, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489832525, "dur":508, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489833033, "dur":445, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489833479, "dur":456, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489833936, "dur":838, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489834775, "dur":1005, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489836257, "dur":528, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.test-framework@388540dd9ce6\\UnityEditor.TestRunner\\TestLaunchers\\Helpers\\AttributeFinderBase.cs" }}
,{ "pid":12345, "tid":10, "ts":1754507489835781, "dur":1111, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489836892, "dur":591, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489837484, "dur":504, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489837989, "dur":703, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1754507489838693, "dur":59, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489838759, "dur":813, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)" }}
,{ "pid":12345, "tid":10, "ts":1754507489839572, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489839712, "dur":205, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1754507489839969, "dur":593, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)" }}
,{ "pid":12345, "tid":10, "ts":1754507489840563, "dur":708, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489841305, "dur":110, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489841418, "dur":168, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Mirror.Transports.dll.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1754507489841615, "dur":744, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Unified.dll.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1754507489842360, "dur":324, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489842690, "dur":735, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Unified.dll (+2 others)" }}
,{ "pid":12345, "tid":10, "ts":1754507489843426, "dur":359, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489843791, "dur":722, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":10, "ts":1754507489844514, "dur":88, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489844651, "dur":977, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Mirror.Examples.dll (+2 others)" }}
,{ "pid":12345, "tid":10, "ts":1754507489845629, "dur":158, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489845790, "dur":955, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489846745, "dur":279, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489847024, "dur":3494, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489850520, "dur":229, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1754507489850765, "dur":62640, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489913408, "dur":2678, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/kcp2k.dll (+pdb)" }}
,{ "pid":12345, "tid":10, "ts":1754507489916087, "dur":1371, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489917467, "dur":3189, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)" }}
,{ "pid":12345, "tid":10, "ts":1754507489920657, "dur":86, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489920748, "dur":2779, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)" }}
,{ "pid":12345, "tid":10, "ts":1754507489923528, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489923605, "dur":2993, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":10, "ts":1754507489926646, "dur":3008, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":10, "ts":1754507489929655, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489929718, "dur":3184, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":10, "ts":1754507489932902, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489932968, "dur":3131, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":10, "ts":1754507489936099, "dur":316, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489936424, "dur":1117, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489937551, "dur":280, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489937930, "dur":120, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489938056, "dur":427, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489938493, "dur":655, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489939199, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489939288, "dur":341, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489939635, "dur":82, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489939778, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489939946, "dur":3469, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754507489943416, "dur":1564673, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489776810, "dur":30173, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489806995, "dur":412, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_A8EE771E29308445.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1754507489807467, "dur":141, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489807649, "dur":423, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489808121, "dur":934, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489809076, "dur":167, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489809244, "dur":488, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_E8FF7AECBB376B8C.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1754507489809735, "dur":61, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_C203261CAF21A327.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1754507489809796, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489809874, "dur":88, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489809970, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489810044, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489810153, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489810305, "dur":429, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":11, "ts":1754507489810880, "dur":77, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp2" }}
,{ "pid":12345, "tid":11, "ts":1754507489811906, "dur":1114, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.Workflow.Editor.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":11, "ts":1754507489813022, "dur":702, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489813725, "dur":5058, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":11, "ts":1754507489818787, "dur":895, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489819682, "dur":704, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489820693, "dur":586, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Utilities\\ObjectExtension.cs" }}
,{ "pid":12345, "tid":11, "ts":1754507489820386, "dur":1124, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489821510, "dur":664, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489822175, "dur":657, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489822833, "dur":617, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489823450, "dur":679, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489824129, "dur":1030, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489825160, "dur":768, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489825928, "dur":662, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489826590, "dur":668, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489827259, "dur":693, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489827953, "dur":635, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489828588, "dur":669, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489829257, "dur":707, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489829965, "dur":697, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489830663, "dur":656, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489831320, "dur":704, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489832024, "dur":627, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489832973, "dur":675, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\InputSystem\\Plugins\\HID\\HIDDescriptorWindow.cs" }}
,{ "pid":12345, "tid":11, "ts":1754507489832652, "dur":1238, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489834605, "dur":556, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\Views\\DownloadPlasticExeDialog.cs" }}
,{ "pid":12345, "tid":11, "ts":1754507489833890, "dur":1386, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489835276, "dur":411, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489835687, "dur":605, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489836320, "dur":508, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489836828, "dur":650, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489837480, "dur":567, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489838049, "dur":275, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1754507489838325, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489838389, "dur":1159, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)" }}
,{ "pid":12345, "tid":11, "ts":1754507489839549, "dur":122, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489839751, "dur":318, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/SimpleWebTransport.dll.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1754507489840070, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489840131, "dur":196, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1754507489840370, "dur":811, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":11, "ts":1754507489841182, "dur":87, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489841394, "dur":215, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1754507489841645, "dur":1734, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":11, "ts":1754507489843380, "dur":495, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489843920, "dur":240, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Mirror.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1754507489844197, "dur":218, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1754507489844416, "dur":170, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489844590, "dur":679, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)" }}
,{ "pid":12345, "tid":11, "ts":1754507489845269, "dur":492, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489845805, "dur":188, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1754507489846031, "dur":479, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":11, "ts":1754507489846553, "dur":159, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489846712, "dur":242, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489846956, "dur":154, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1754507489847139, "dur":418, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)" }}
,{ "pid":12345, "tid":11, "ts":1754507489847558, "dur":255, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489847863, "dur":146, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1754507489848043, "dur":732, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":11, "ts":1754507489848783, "dur":125, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489848951, "dur":87, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1754507489849055, "dur":355, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":11, "ts":1754507489849484, "dur":78, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1754507489849579, "dur":300, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll (+2 others)" }}
,{ "pid":12345, "tid":11, "ts":1754507489849918, "dur":718, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489850637, "dur":62754, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489913395, "dur":4583, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Cinemachine.dll (+pdb)" }}
,{ "pid":12345, "tid":11, "ts":1754507489917979, "dur":898, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489918884, "dur":3830, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)" }}
,{ "pid":12345, "tid":11, "ts":1754507489922715, "dur":1156, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489923877, "dur":2345, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PackageValidationSuite.Editor.Extension.dll (+pdb)" }}
,{ "pid":12345, "tid":11, "ts":1754507489926223, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489926302, "dur":4290, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.PlayMode.Scenarios.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":11, "ts":1754507489930593, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489930683, "dur":2677, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":11, "ts":1754507489933361, "dur":583, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489933952, "dur":5152, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":11, "ts":1754507489939105, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489939326, "dur":212, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507489939955, "dur":1096498, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754507491036535, "dur":470002, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll" }}
,{ "pid":12345, "tid":11, "ts":1754507491036459, "dur":470082, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll" }}
,{ "pid":12345, "tid":11, "ts":1754507491506571, "dur":1383, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/ScriptAssemblies/Assembly-CSharp-Editor.dll" }}
,{ "pid":12345, "tid":12, "ts":1754507489776856, "dur":30168, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754507489807028, "dur":306, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_090E8A4E264B09AD.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1754507489807335, "dur":518, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754507489808098, "dur":398, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754507489808498, "dur":722, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_2AD3F77C8CF90E8B.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1754507489809221, "dur":519, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754507489809740, "dur":1603, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_2AD3F77C8CF90E8B.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1754507489811357, "dur":127, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1754507489811507, "dur":14929, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)" }}
,{ "pid":12345, "tid":12, "ts":1754507489826437, "dur":1860, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754507489828369, "dur":146, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1754507489828515, "dur":75, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754507489828593, "dur":8642, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)" }}
,{ "pid":12345, "tid":12, "ts":1754507489837385, "dur":88, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1754507489837487, "dur":315, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)" }}
,{ "pid":12345, "tid":12, "ts":1754507489837888, "dur":135, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1754507489838023, "dur":357, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754507489838383, "dur":1204, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)" }}
,{ "pid":12345, "tid":12, "ts":1754507489839588, "dur":471, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754507489840109, "dur":196, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1754507489840305, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754507489840379, "dur":817, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)" }}
,{ "pid":12345, "tid":12, "ts":1754507489841197, "dur":237, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754507489841444, "dur":192, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Mirror.Components.dll.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1754507489841659, "dur":884, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Mirror.Components.dll (+2 others)" }}
,{ "pid":12345, "tid":12, "ts":1754507489842544, "dur":131, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754507489842683, "dur":718, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)" }}
,{ "pid":12345, "tid":12, "ts":1754507489843402, "dur":508, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754507489843917, "dur":222, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Mirror.Examples.dll.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1754507489844180, "dur":304, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/EncryptionTransportEditor.dll.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1754507489844521, "dur":654, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/EncryptionTransportEditor.dll (+2 others)" }}
,{ "pid":12345, "tid":12, "ts":1754507489845176, "dur":110, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754507489845291, "dur":242, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.022.dll.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1754507489845571, "dur":610, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.022.dll (+2 others)" }}
,{ "pid":12345, "tid":12, "ts":1754507489846182, "dur":259, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754507489846444, "dur":287, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754507489846731, "dur":282, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754507489847014, "dur":1768, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754507489848783, "dur":172, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1754507489848971, "dur":963, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":12, "ts":1754507489849991, "dur":64, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1754507489850067, "dur":374, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":12, "ts":1754507489850515, "dur":130, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1754507489850694, "dur":301, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll (+2 others)" }}
,{ "pid":12345, "tid":12, "ts":1754507489850996, "dur":65, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754507489851064, "dur":63818, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754507489914884, "dur":2308, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.dll (+pdb)" }}
,{ "pid":12345, "tid":12, "ts":1754507489917194, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754507489917282, "dur":4154, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)" }}
,{ "pid":12345, "tid":12, "ts":1754507489921437, "dur":196, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754507489921656, "dur":3973, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)" }}
,{ "pid":12345, "tid":12, "ts":1754507489925630, "dur":664, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754507489926305, "dur":4265, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.022.dll (+pdb)" }}
,{ "pid":12345, "tid":12, "ts":1754507489930570, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754507489930653, "dur":4506, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PackageValidationSuite.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":12, "ts":1754507489935166, "dur":289, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754507489935463, "dur":2891, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Playmode.VirtualProjects.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":12, "ts":1754507489938355, "dur":152, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754507489938548, "dur":105, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754507489938719, "dur":257, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754507489938980, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754507489939030, "dur":334, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/Unity.AdaptivePerformance.UI.Editor.dll" }}
,{ "pid":12345, "tid":12, "ts":1754507489939380, "dur":115, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754507489939500, "dur":354, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.dll" }}
,{ "pid":12345, "tid":12, "ts":1754507489942126, "dur":214, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":12, "ts":1754507489942340, "dur":968, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner" }}
,{ "pid":12345, "tid":12, "ts":1754507489943308, "dur":54, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger" }}
,{ "pid":12345, "tid":12, "ts":1754507489939974, "dur":3391, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754507489943366, "dur":1564730, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489776892, "dur":30136, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489807032, "dur":340, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_18C55C75F47E4EB6.mvfrm" }}
,{ "pid":12345, "tid":13, "ts":1754507489807373, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489807460, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489807551, "dur":797, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489808369, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489808628, "dur":125, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489808756, "dur":362, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_10364B2612418FBF.mvfrm" }}
,{ "pid":12345, "tid":13, "ts":1754507489809660, "dur":234, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489810014, "dur":263, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489810281, "dur":50, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp" }}
,{ "pid":12345, "tid":13, "ts":1754507489810347, "dur":1635, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp" }}
,{ "pid":12345, "tid":13, "ts":1754507489811997, "dur":440, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489812492, "dur":323, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489812893, "dur":331, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489813281, "dur":532, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Mirror.Components.rsp" }}
,{ "pid":12345, "tid":13, "ts":1754507489813816, "dur":67, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489813890, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489814023, "dur":53, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp" }}
,{ "pid":12345, "tid":13, "ts":1754507489814077, "dur":56, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489814138, "dur":104, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489814247, "dur":63, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489814315, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489814496, "dur":1465, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/10142702499866438521.rsp" }}
,{ "pid":12345, "tid":13, "ts":1754507489815962, "dur":317, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489816279, "dur":301, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489816581, "dur":302, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489816883, "dur":333, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489817216, "dur":399, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489817616, "dur":326, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489817942, "dur":601, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489818543, "dur":490, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489819034, "dur":794, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489819829, "dur":728, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489820704, "dur":2584, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\treeview\\Drawers\\InfiniteTrackDrawer.cs" }}
,{ "pid":12345, "tid":13, "ts":1754507489820558, "dur":3048, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489823607, "dur":468, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489824076, "dur":452, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489824528, "dur":498, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489825026, "dur":517, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489825544, "dur":463, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489826007, "dur":480, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489826487, "dur":474, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489826961, "dur":461, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489827422, "dur":497, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489827920, "dur":555, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489829293, "dur":1058, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.core@b1fa9fba9fd6\\Runtime\\Lights\\LightAnchor.cs" }}
,{ "pid":12345, "tid":13, "ts":1754507489828475, "dur":1955, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489830430, "dur":491, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489830921, "dur":502, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489831423, "dur":610, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489832034, "dur":577, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489832990, "dur":1124, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\InputSystem\\Plugins\\OnScreen\\OnScreenSupport.cs" }}
,{ "pid":12345, "tid":13, "ts":1754507489832611, "dur":1637, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489834249, "dur":1029, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489835331, "dur":962, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489836383, "dur":373, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489836757, "dur":567, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489837324, "dur":701, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489838026, "dur":244, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Editor.Bridge.dll.mvfrm" }}
,{ "pid":12345, "tid":13, "ts":1754507489838270, "dur":1625, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489839900, "dur":1042, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Editor.Bridge.dll (+2 others)" }}
,{ "pid":12345, "tid":13, "ts":1754507489840943, "dur":764, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489841753, "dur":300, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Configurations.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":13, "ts":1754507489842108, "dur":981, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Configurations.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":13, "ts":1754507489843090, "dur":743, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489843844, "dur":603, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489844452, "dur":267, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Scenarios.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":13, "ts":1754507489844766, "dur":1239, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Scenarios.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":13, "ts":1754507489846005, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489846124, "dur":579, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489846703, "dur":256, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489846960, "dur":208, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":13, "ts":1754507489847222, "dur":693, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":13, "ts":1754507489847963, "dur":93, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":13, "ts":1754507489848075, "dur":317, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":13, "ts":1754507489848393, "dur":198, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489848617, "dur":1980, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489850598, "dur":64349, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489914948, "dur":2910, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Playmode.Common.Runtime.dll (+pdb)" }}
,{ "pid":12345, "tid":13, "ts":1754507489917860, "dur":271, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489918169, "dur":3752, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.Unified.dll (+pdb)" }}
,{ "pid":12345, "tid":13, "ts":1754507489921922, "dur":1366, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489923295, "dur":2915, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)" }}
,{ "pid":12345, "tid":13, "ts":1754507489926216, "dur":1171, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489927395, "dur":2504, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":13, "ts":1754507489929900, "dur":118, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489930026, "dur":2593, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":13, "ts":1754507489932620, "dur":1188, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489933817, "dur":3461, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Mirror.CompilerSymbols.dll (+pdb)" }}
,{ "pid":12345, "tid":13, "ts":1754507489937278, "dur":112, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489937397, "dur":1279, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489938735, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489938816, "dur":552, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489939369, "dur":57, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.pdb" }}
,{ "pid":12345, "tid":13, "ts":1754507489939430, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489939841, "dur":140, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1754507489939984, "dur":1568240, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1754507489776954, "dur":30114, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1754507489807075, "dur":323, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_34C7DB8E4ECB1B7B.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1754507489807398, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1754507489807486, "dur":86, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_DDA8E091E3967B34.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1754507489807572, "dur":319, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1754507489807949, "dur":53, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_674D8A39E28088FE.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1754507489808112, "dur":940, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1754507489809074, "dur":407, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1754507489809502, "dur":174, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1754507489809677, "dur":92, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_40911F3A652DB05F.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1754507489809773, "dur":96, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.APIComparison.Framework.dll_795DFBA713DFDA95.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1754507489809870, "dur":86, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1754507489809975, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1754507489810052, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1754507489810127, "dur":228, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1754507489810648, "dur":2326, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":14, "ts":1754507489812975, "dur":745, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1754507489813721, "dur":5398, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Extension.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":14, "ts":1754507489820309, "dur":4660, "ph":"X", "name": "File",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.47f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-core-rtlsupport-l1-1-0.dll" }}
,{ "pid":12345, "tid":14, "ts":1754507489819121, "dur":5903, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1754507489825246, "dur":2252, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.universal@39bda2df468a\\Runtime\\ShaderData.cs" }}
,{ "pid":12345, "tid":14, "ts":1754507489825024, "dur":2789, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1754507489827813, "dur":460, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1754507489828273, "dur":456, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1754507489828729, "dur":572, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1754507489829301, "dur":534, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1754507489829836, "dur":535, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1754507489830371, "dur":488, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1754507489830859, "dur":475, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1754507489831334, "dur":488, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1754507489832017, "dur":1016, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Runtime\\TimelineClip.cs" }}
,{ "pid":12345, "tid":14, "ts":1754507489831822, "dur":1575, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1754507489833397, "dur":454, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1754507489833851, "dur":939, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1754507489835118, "dur":1007, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\AssetsUtils\\SaveAssets.cs" }}
,{ "pid":12345, "tid":14, "ts":1754507489834791, "dur":2376, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1754507489837167, "dur":315, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1754507489837483, "dur":476, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1754507489837961, "dur":266, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1754507489838228, "dur":59, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1754507489838293, "dur":2271, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)" }}
,{ "pid":12345, "tid":14, "ts":1754507489840565, "dur":197, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1754507489840821, "dur":234, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1754507489841055, "dur":284, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1754507489841343, "dur":2194, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)" }}
,{ "pid":12345, "tid":14, "ts":1754507489843538, "dur":662, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1754507489844248, "dur":164, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1754507489844413, "dur":1073, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1754507489845489, "dur":1131, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)" }}
,{ "pid":12345, "tid":14, "ts":1754507489846696, "dur":115, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.dll.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1754507489846833, "dur":565, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.dll (+2 others)" }}
,{ "pid":12345, "tid":14, "ts":1754507489847398, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1754507489847511, "dur":184, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1754507489847717, "dur":474, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":14, "ts":1754507489848191, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1754507489848287, "dur":131, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1754507489848441, "dur":566, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":14, "ts":1754507489849008, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1754507489849108, "dur":92, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Extension.dll.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1754507489849212, "dur":305, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Extension.dll (+2 others)" }}
,{ "pid":12345, "tid":14, "ts":1754507489849581, "dur":1085, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1754507489850666, "dur":62743, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1754507489913417, "dur":5310, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Mirror.Components.dll (+pdb)" }}
,{ "pid":12345, "tid":14, "ts":1754507489918729, "dur":99, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1754507489918840, "dur":4397, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)" }}
,{ "pid":12345, "tid":14, "ts":1754507489923243, "dur":755, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1754507489924005, "dur":2521, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)" }}
,{ "pid":12345, "tid":14, "ts":1754507489926527, "dur":216, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1754507489926750, "dur":2637, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":14, "ts":1754507489929388, "dur":4152, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1754507489933547, "dur":2166, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mobile.AndroidLogcat.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":14, "ts":1754507489935714, "dur":142, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1754507489935864, "dur":3993, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":14, "ts":1754507489939857, "dur":111, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1754507489940027, "dur":1568071, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1754507489776985, "dur":30122, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1754507489807110, "dur":470, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_48A48871E8231606.mvfrm" }}
,{ "pid":12345, "tid":15, "ts":1754507489807580, "dur":281, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1754507489808393, "dur":62, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_8784B94BF851BB6E.mvfrm" }}
,{ "pid":12345, "tid":15, "ts":1754507489808455, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1754507489808661, "dur":123, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1754507489808992, "dur":747, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1754507489809740, "dur":918, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_C2658D8F62234B56.mvfrm" }}
,{ "pid":12345, "tid":15, "ts":1754507489810774, "dur":134, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp" }}
,{ "pid":12345, "tid":15, "ts":1754507489810965, "dur":58, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp" }}
,{ "pid":12345, "tid":15, "ts":1754507489811294, "dur":1625, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":15, "ts":1754507489812921, "dur":353, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1754507489813689, "dur":655, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PackageValidationSuite.Editor.Extension.rsp" }}
,{ "pid":12345, "tid":15, "ts":1754507489815044, "dur":73, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/2781082588993088484.rsp" }}
,{ "pid":12345, "tid":15, "ts":1754507489815261, "dur":1028, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/11655734795442284912.rsp" }}
,{ "pid":12345, "tid":15, "ts":1754507489816291, "dur":516, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1754507489816808, "dur":379, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1754507489817187, "dur":320, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1754507489817507, "dur":345, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1754507489817852, "dur":519, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1754507489818371, "dur":525, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1754507489818896, "dur":895, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1754507489819791, "dur":508, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1754507489820300, "dur":4235, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.collections@56bff8827a7e\\Unity.Collections.Editor\\JobsMenu.cs" }}
,{ "pid":12345, "tid":15, "ts":1754507489820300, "dur":4709, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1754507489825010, "dur":463, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1754507489825473, "dur":502, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1754507489826026, "dur":1485, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.adaptiveperformance@ffde895a1c6a\\Editor\\Analytics\\AdaptivePerformanceAnalyticsEvent.cs" }}
,{ "pid":12345, "tid":15, "ts":1754507489825976, "dur":1929, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1754507489828346, "dur":985, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.ugui@f3fac7af1578\\Runtime\\TMP\\TMP_UpdateManager.cs" }}
,{ "pid":12345, "tid":15, "ts":1754507489827906, "dur":1425, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1754507489829332, "dur":672, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1754507489830005, "dur":581, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1754507489830586, "dur":506, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1754507489831093, "dur":529, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1754507489831622, "dur":962, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1754507489832584, "dur":925, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1754507489833509, "dur":457, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1754507489833966, "dur":848, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1754507489835012, "dur":847, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\AssetOverlays\\Cache\\AssetStatusCache.cs" }}
,{ "pid":12345, "tid":15, "ts":1754507489834814, "dur":2122, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1754507489836936, "dur":461, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1754507489837397, "dur":567, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1754507489837966, "dur":236, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.Common.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":15, "ts":1754507489838253, "dur":1170, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.Common.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":15, "ts":1754507489839424, "dur":167, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1754507489839642, "dur":369, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.UI.Editor.dll.mvfrm" }}
,{ "pid":12345, "tid":15, "ts":1754507489840040, "dur":615, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.UI.Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":15, "ts":1754507489840656, "dur":929, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1754507489841612, "dur":236, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.dll.mvfrm" }}
,{ "pid":12345, "tid":15, "ts":1754507489841874, "dur":173, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm" }}
,{ "pid":12345, "tid":15, "ts":1754507489842048, "dur":1321, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1754507489843375, "dur":871, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)" }}
,{ "pid":12345, "tid":15, "ts":1754507489844247, "dur":118, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1754507489844375, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1754507489844445, "dur":704, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Mirror.Authenticators.dll (+2 others)" }}
,{ "pid":12345, "tid":15, "ts":1754507489845150, "dur":550, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1754507489845799, "dur":931, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1754507489846731, "dur":225, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1754507489846957, "dur":161, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm" }}
,{ "pid":12345, "tid":15, "ts":1754507489847144, "dur":472, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)" }}
,{ "pid":12345, "tid":15, "ts":1754507489847617, "dur":94, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1754507489847743, "dur":2773, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1754507489850517, "dur":127, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm" }}
,{ "pid":12345, "tid":15, "ts":1754507489850644, "dur":120, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1754507489850794, "dur":243, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)" }}
,{ "pid":12345, "tid":15, "ts":1754507489851082, "dur":218, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)" }}
,{ "pid":12345, "tid":15, "ts":1754507489851442, "dur":273184, "ph":"X", "name": "Csc",  "args": { "detail":"Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)" }}
,{ "pid":12345, "tid":15, "ts":1754507490128687, "dur":63472, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll" }}
,{ "pid":12345, "tid":15, "ts":1754507490128259, "dur":63993, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":15, "ts":1754507490192632, "dur":76, "ph":"X", "name": "StoreTimestampsOfNonGeneratedInputFiles",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1754507490193297, "dur":230900, "ph":"X", "name": "Csc",  "args": { "detail":"Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)" }}
,{ "pid":12345, "tid":15, "ts":1754507490428134, "dur":368754, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp-Editor.dll" }}
,{ "pid":12345, "tid":15, "ts":1754507490428118, "dur":370046, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":15, "ts":1754507490799162, "dur":217, "ph":"X", "name": "StoreTimestampsOfNonGeneratedInputFiles",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1754507490799707, "dur":220830, "ph":"X", "name": "ILPostProcess",  "args": { "detail":"Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":15, "ts":1754507491036440, "dur":143, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb" }}
,{ "pid":12345, "tid":15, "ts":1754507491036422, "dur":163, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb" }}
,{ "pid":12345, "tid":15, "ts":1754507491036611, "dur":1988, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb" }}
,{ "pid":12345, "tid":15, "ts":1754507491038604, "dur":469476, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489777009, "dur":30105, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489807115, "dur":520, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_915E418E1D60FCA1.mvfrm" }}
,{ "pid":12345, "tid":16, "ts":1754507489807636, "dur":182, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489807820, "dur":1345, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_8029272894F862A3.mvfrm" }}
,{ "pid":12345, "tid":16, "ts":1754507489809166, "dur":75, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489809241, "dur":204, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_8029272894F862A3.mvfrm" }}
,{ "pid":12345, "tid":16, "ts":1754507489809448, "dur":53, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_DD8A67269492BAE1.mvfrm" }}
,{ "pid":12345, "tid":16, "ts":1754507489809501, "dur":210, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489809711, "dur":1077, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_DD8A67269492BAE1.mvfrm" }}
,{ "pid":12345, "tid":16, "ts":1754507489810807, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489810877, "dur":56, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.Common.Editor.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":16, "ts":1754507489810935, "dur":72, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp" }}
,{ "pid":12345, "tid":16, "ts":1754507489811008, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489811322, "dur":1635, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":16, "ts":1754507489812958, "dur":269, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489813681, "dur":196, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Extension.rsp" }}
,{ "pid":12345, "tid":16, "ts":1754507489813879, "dur":111, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489814317, "dur":100, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489814486, "dur":1437, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.WorkflowUI.Editor.rsp" }}
,{ "pid":12345, "tid":16, "ts":1754507489815924, "dur":306, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489816230, "dur":301, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489816531, "dur":340, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489816872, "dur":631, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489817504, "dur":630, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489818194, "dur":550, "ph":"X", "name": "File",  "args": { "detail":"Assets\\Mirror\\Hosting\\Edgegap\\Editor\\Api\\Models\\Requests\\CreateAppRequest.cs" }}
,{ "pid":12345, "tid":16, "ts":1754507489818134, "dur":1872, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489820684, "dur":599, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.multiplayer.center@f3fb577b3546\\Editor\\AssemblyInfo.cs" }}
,{ "pid":12345, "tid":16, "ts":1754507489820006, "dur":1604, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489821611, "dur":585, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489822196, "dur":702, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489822898, "dur":662, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489823561, "dur":619, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489824181, "dur":633, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489824814, "dur":690, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489825504, "dur":694, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489826198, "dur":652, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489826851, "dur":662, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489827513, "dur":660, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489828173, "dur":651, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489828920, "dur":549, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.render-pipelines.core@b1fa9fba9fd6\\Runtime\\Common\\GlobalDynamicResolutionSettings.cs" }}
,{ "pid":12345, "tid":16, "ts":1754507489828824, "dur":1253, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489830078, "dur":756, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489830834, "dur":684, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489831518, "dur":809, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489832328, "dur":1253, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489833582, "dur":651, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489834233, "dur":1405, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489835639, "dur":1549, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489837188, "dur":199, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489837387, "dur":665, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489838053, "dur":229, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Android.dll.mvfrm" }}
,{ "pid":12345, "tid":16, "ts":1754507489838327, "dur":505, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm" }}
,{ "pid":12345, "tid":16, "ts":1754507489838833, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489838903, "dur":1171, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)" }}
,{ "pid":12345, "tid":16, "ts":1754507489840075, "dur":2135, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489842276, "dur":344, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm" }}
,{ "pid":12345, "tid":16, "ts":1754507489842621, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489842679, "dur":1212, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)" }}
,{ "pid":12345, "tid":16, "ts":1754507489843892, "dur":130, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489844035, "dur":534, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Edgegap.dll.mvfrm" }}
,{ "pid":12345, "tid":16, "ts":1754507489844610, "dur":1219, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Edgegap.dll (+2 others)" }}
,{ "pid":12345, "tid":16, "ts":1754507489845829, "dur":532, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489846368, "dur":366, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489846735, "dur":222, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489846958, "dur":149, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm" }}
,{ "pid":12345, "tid":16, "ts":1754507489847107, "dur":127, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489847237, "dur":502, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":16, "ts":1754507489847739, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489847845, "dur":2792, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489850637, "dur":62776, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489913422, "dur":3793, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)" }}
,{ "pid":12345, "tid":16, "ts":1754507489917216, "dur":270, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489917538, "dur":2960, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Telepathy.dll (+pdb)" }}
,{ "pid":12345, "tid":16, "ts":1754507489920499, "dur":98, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489920605, "dur":3149, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)" }}
,{ "pid":12345, "tid":16, "ts":1754507489923761, "dur":461, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489924230, "dur":3731, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)" }}
,{ "pid":12345, "tid":16, "ts":1754507489927962, "dur":81, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489928052, "dur":3804, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":16, "ts":1754507489931857, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489931941, "dur":5404, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)" }}
,{ "pid":12345, "tid":16, "ts":1754507489937346, "dur":91, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489937491, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489937558, "dur":258, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489938004, "dur":59, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489938082, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489938479, "dur":196, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/Unity.Collections.dll" }}
,{ "pid":12345, "tid":16, "ts":1754507489938676, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489938809, "dur":148, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489939037, "dur":385, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles Library/ScriptAssemblies/Mirror.Editor.pdb" }}
,{ "pid":12345, "tid":16, "ts":1754507489939428, "dur":81, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489939515, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489939804, "dur":75, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1754507489939983, "dur":1568059, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754507491529156, "dur":3004, "ph":"X", "name": "ProfilerWriteOutput" }
,