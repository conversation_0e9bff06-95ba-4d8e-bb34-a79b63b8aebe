using UnityEngine;
using UnityEditor;

[CustomEditor(typeof(TasksManager))]public class TasksManagerEditor : Editor
{
    // Variables for task creation
    private string newTaskName = "New Task";
    private TaskType newTaskType = TaskType.Quiz;
    private int newTaskBuildingIndex = 0;
    private Vector3 newTaskPosition = Vector3.zero;

    // Foldout states for collapsible sections
    private bool showAddNewTask = true;
    private bool showTasks = true;
    private bool showManageTypes = false;

    // Variables for type management
    private string newTypeName = "";

    public override void OnInspectorGUI()
    {
        DrawDefaultInspector();

        TasksManager tasksManager = (TasksManager)target;

        if (GUILayout.Button("Reload Tasks"))
        {
            tasksManager.LoadDataFromChildren();
        }

        if (GUILayout.Button("Reset Task Done"))
        {
            Tasks.taskCount = 0;
        }

        GUILayout.Label("Task Done: " + Tasks.taskCount);

        // Get building manager for dropdown options (used by multiple sections)
        BuildingManager buildingManager = FindFirstObjectByType<BuildingManager>();
        string[] buildingNames = new string[0];

        if (buildingManager != null)
        {
            var buildingDataList = buildingManager.GetAllBuildingData();
            buildingNames = new string[buildingDataList.Count];
            for (int i = 1; i < buildingDataList.Count; i++)
            {
                buildingNames[i] = buildingDataList[i].buildingName;
            }
        }

        // Add Task Creation Section
        GUILayout.Space(10);
        showAddNewTask = EditorGUILayout.Foldout(showAddNewTask, "Add New Task", true, EditorStyles.foldoutHeader);

        if (showAddNewTask)
        {

            EditorGUILayout.BeginVertical("box");

        // Task name input
        EditorGUILayout.BeginHorizontal();
        GUILayout.Label("Task Name:", GUILayout.Width(80));
        newTaskName = EditorGUILayout.TextField(newTaskName);
        EditorGUILayout.EndHorizontal();

        // Task type selection
        EditorGUILayout.BeginHorizontal();
        GUILayout.Label("Task Type:", GUILayout.Width(80));
        newTaskType = (TaskType)EditorGUILayout.EnumPopup(newTaskType);
        EditorGUILayout.EndHorizontal();

        // Building selection
        if (buildingNames.Length > 0)
        {
            EditorGUILayout.BeginHorizontal();
            GUILayout.Label("Building:", GUILayout.Width(80));
            newTaskBuildingIndex = EditorGUILayout.Popup(newTaskBuildingIndex, buildingNames);
            EditorGUILayout.EndHorizontal();
        }
        else
        {
            EditorGUILayout.HelpBox("No buildings found. Make sure BuildingManager exists in the scene.", MessageType.Warning);
        }

        GUILayout.Space(5);

        // Add Task button
        GUI.enabled = !string.IsNullOrEmpty(newTaskName) && buildingNames.Length > 0;
        if (GUILayout.Button("Add Task", GUILayout.Height(25)))
        {
            newTaskPosition = buildingManager.buildings[newTaskBuildingIndex].transform.position;
            CreateNewTask(tasksManager, newTaskName, newTaskType, newTaskBuildingIndex, newTaskPosition);
        }
        GUI.enabled = true;

            EditorGUILayout.EndVertical();
        }

        // Display tasks in a tab-like interface
        if (tasksManager.tasks != null && tasksManager.tasks.Length > 0)
        {
            GUILayout.Space(10);
            showTasks = EditorGUILayout.Foldout(showTasks, "Tasks:", true, EditorStyles.foldoutHeader);

            if (showTasks)
            {

            // Display each task in a tab-like format
            for (int i = 0; i < tasksManager.tasks.Length; i++)
            {
                Tasks task = tasksManager.tasks[i];
                if (task == null) continue;

                // Create a box for each task (tab-like appearance)
                EditorGUILayout.BeginVertical("box");

                // Task header with name
                EditorGUILayout.BeginHorizontal();
                GUILayout.Label($"Task {i + 1}: {task.name}", EditorStyles.boldLabel);
                GUILayout.FlexibleSpace();
                GUILayout.Label($"Type:", GUILayout.Width(35));
                TaskType newTaskType = (TaskType)EditorGUILayout.EnumPopup(task.taskType, GUILayout.Width(80));

                if (newTaskType != task.taskType)
                {
                    Undo.RecordObject(task, "Change Task Type");
                    task.taskType = newTaskType;
                    EditorUtility.SetDirty(task);
                }
                EditorGUILayout.EndHorizontal();

                // Task controls row
                EditorGUILayout.BeginHorizontal();

                // Building selection dropdown
                if (buildingNames.Length > 0)
                {
                    GUILayout.Label("Building:", GUILayout.Width(60));
                    int currentBuildingIndex = Mathf.Clamp(task.buildingIndex, 0, buildingNames.Length - 1);
                    int newBuildingIndex = EditorGUILayout.Popup(currentBuildingIndex, buildingNames, GUILayout.Width(120));

                    if (newBuildingIndex != task.buildingIndex)
                    {
                        Undo.RecordObject(task, "Change Task Building");
                        task.buildingIndex = newBuildingIndex;
                        EditorUtility.SetDirty(task);
                    }
                }
                else
                {
                    GUILayout.Label("No buildings found", GUILayout.Width(120));
                }

                GUILayout.FlexibleSpace();

                // Task action buttons
                if (GUILayout.Button("Complete", GUILayout.Width(70)))
                {
                    if (EditorUtility.DisplayDialog("Complete Task",
                        $"Are you sure you want to complete task '{task.name}'?",
                        "Yes", "No"))
                    {
                        task.CompleteTask();
                        tasksManager.LoadDataFromChildren(); // Refresh the task list
                    }
                }

                if (GUILayout.Button("Delete", GUILayout.Width(60)))
                {
                    if (EditorUtility.DisplayDialog("Delete Task",
                        $"Are you sure you want to delete task '{task.name}'?",
                        "Yes", "No"))
                    {
                        Undo.DestroyObjectImmediate(task.gameObject);
                        tasksManager.LoadDataFromChildren(); // Refresh the task list
                    }
                }

                EditorGUILayout.EndHorizontal();

                // Additional task info
                EditorGUILayout.BeginHorizontal();
                GUILayout.Label($"Building Index: {task.buildingIndex}", EditorStyles.miniLabel);
                GUILayout.FlexibleSpace();
                GUILayout.Label($"Position: {task.transform.position}", EditorStyles.miniLabel);
                EditorGUILayout.EndHorizontal();

                EditorGUILayout.EndVertical();
                GUILayout.Space(5);
            }
            }
        }
        else
        {
            GUILayout.Space(10);
            EditorGUILayout.HelpBox("No tasks found. Make sure tasks are children of this TasksManager.", MessageType.Info);
        }

        // Manage Types Section
        GUILayout.Space(10);
        showManageTypes = EditorGUILayout.Foldout(showManageTypes, "Manage Types", true, EditorStyles.foldoutHeader);

        if (showManageTypes)
        {
            EditorGUILayout.BeginVertical("box");

            // Display current types
            GUILayout.Label("Current Task Types:", EditorStyles.boldLabel);

            string[] enumNames = System.Enum.GetNames(typeof(TaskType));
            for (int i = 0; i < enumNames.Length; i++)
            {
                EditorGUILayout.BeginHorizontal();
                GUILayout.Label($"{i + 1}. {enumNames[i]}", GUILayout.ExpandWidth(true));

                if (GUILayout.Button("Delete", GUILayout.Width(60)))
                {
                    if (EditorUtility.DisplayDialog("Delete Task Type",
                        $"Are you sure you want to delete task type '{enumNames[i]}'?\n\nThis will require manual cleanup of the TaskType enum in Tasks.cs",
                        "Yes", "No"))
                    {
                        Debug.LogWarning($"Please manually remove '{enumNames[i]}' from the TaskType enum in Assets/Tasks.cs");
                        EditorUtility.DisplayDialog("Manual Action Required",
                            $"Please manually remove '{enumNames[i]}' from the TaskType enum in Assets/Tasks.cs\n\nAlso delete the corresponding script: Assets/Tasks/{enumNames[i]}.cs",
                            "OK");
                    }
                }
                EditorGUILayout.EndHorizontal();
            }

            GUILayout.Space(10);

            // Add new type section
            GUILayout.Label("Add New Task Type:", EditorStyles.boldLabel);
            EditorGUILayout.BeginHorizontal();
            GUILayout.Label("Type Name:", GUILayout.Width(80));
            newTypeName = EditorGUILayout.TextField(newTypeName);
            EditorGUILayout.EndHorizontal();

            GUILayout.Space(5);

            GUI.enabled = !string.IsNullOrEmpty(newTypeName) && !string.IsNullOrWhiteSpace(newTypeName);
            if (GUILayout.Button("Add Task Type", GUILayout.Height(25)))
            {
                AddNewTaskType(newTypeName.Trim());
            }
            GUI.enabled = true;

            EditorGUILayout.EndVertical();
        }
    }

    /// <summary>
    /// Creates a new task as a child of the TasksManager
    /// </summary>
    private void CreateNewTask(TasksManager tasksManager, string taskName, TaskType taskType, int buildingIndex, Vector3 position)
    {
        // Create new GameObject for the task
        GameObject taskObject = new GameObject(taskName);

        // Set it as a child of the TasksManager
        taskObject.transform.SetParent(tasksManager.transform);
        taskObject.transform.position = position;

        // Add the Tasks component
        Tasks taskComponent = taskObject.AddComponent<Tasks>();

        // Set task properties
        taskComponent.taskType = taskType;
        taskComponent.buildingIndex = Mathf.Clamp(buildingIndex, 0, int.MaxValue);

        // Register undo operation
        Undo.RegisterCreatedObjectUndo(taskObject, "Create New Task");

        // Mark the TasksManager as dirty to ensure changes are saved
        EditorUtility.SetDirty(tasksManager);

        // Refresh the task list
        tasksManager.LoadDataFromChildren();

        // Reset the input fields for next task
        newTaskName = "New Task";
        newTaskType = TaskType.Quiz;
        newTaskBuildingIndex = 0;
        newTaskPosition = Vector3.zero;

        Debug.Log($"Created new task: {taskName} with type {taskType} at position {position}");
    }

    /// <summary>
    /// Adds a new task type by modifying the TaskType enum and creating the corresponding script
    /// </summary>
    private void AddNewTaskType(string typeName)
    {
        // Validate the type name
        if (string.IsNullOrEmpty(typeName) || !System.Text.RegularExpressions.Regex.IsMatch(typeName, @"^[A-Za-z][A-Za-z0-9_]*$"))
        {
            EditorUtility.DisplayDialog("Invalid Type Name",
                "Type name must start with a letter and contain only letters, numbers, and underscores.",
                "OK");
            return;
        }

        // Check if type already exists
        if (System.Enum.IsDefined(typeof(TaskType), typeName))
        {
            EditorUtility.DisplayDialog("Type Already Exists",
                $"Task type '{typeName}' already exists.",
                "OK");
            return;
        }

        // Show instructions for manual enum modification
        EditorUtility.DisplayDialog("Manual Action Required",
            $"To add the new task type '{typeName}':\n\n" +
            "1. Open Assets/Tasks.cs\n" +
            "2. Add '{typeName}' to the TaskType enum\n" +
            "3. A script will be automatically created in Assets/Tasks/{typeName}.cs\n\n" +
            "Click OK to create the script file.",
            "OK");

        // Create the script file
        CreateTaskTypeScript(typeName);

        // Reset the input field
        newTypeName = "";

        Debug.Log($"Instructions provided for adding new task type: {typeName}");
    }

    /// <summary>
    /// Creates a new task type script file
    /// </summary>
    private void CreateTaskTypeScript(string typeName)
    {
        string tasksDirectoryPath = "Assets/Tasks";
        if (!System.IO.Directory.Exists(tasksDirectoryPath))
        {
            System.IO.Directory.CreateDirectory(tasksDirectoryPath);
            AssetDatabase.Refresh();
        }

        string scriptPath = $"{tasksDirectoryPath}/{typeName}.cs";

        if (System.IO.File.Exists(scriptPath))
        {
            Debug.LogWarning($"Script file already exists: {scriptPath}");
            return;
        }

        string scriptContent = $@"using UnityEngine;

public class {typeName} : MonoBehaviour
{{
    // Start is called before the first frame update
    void Start()
    {{

    }}

    // Update is called once per frame
    void Update()
    {{

    }}
}}";

        System.IO.File.WriteAllText(scriptPath, scriptContent);
        AssetDatabase.Refresh();

        Debug.Log($"Created script file: {scriptPath}");
    }
}