{"format": 1, "restore": {"c:\\Users\\<USER>\\ONU\\kcp2k.csproj": {}}, "projects": {"c:\\Users\\<USER>\\ONU\\kcp2k.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "c:\\Users\\<USER>\\ONU\\kcp2k.csproj", "projectName": "kcp2k", "projectPath": "c:\\Users\\<USER>\\ONU\\kcp2k.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "c:\\Users\\<USER>\\ONU\\Temp\\obj\\kcp2k\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"c:\\Users\\<USER>\\ONU\\Mirror.csproj": {"projectPath": "c:\\Users\\<USER>\\ONU\\Mirror.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.205\\RuntimeIdentifierGraph.json"}}}, "c:\\Users\\<USER>\\ONU\\Mirror.CompilerSymbols.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "c:\\Users\\<USER>\\ONU\\Mirror.CompilerSymbols.csproj", "projectName": "Mirror.CompilerSymbols", "projectPath": "c:\\Users\\<USER>\\ONU\\Mirror.CompilerSymbols.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "c:\\Users\\<USER>\\ONU\\Temp\\obj\\Mirror.CompilerSymbols\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.205\\RuntimeIdentifierGraph.json"}}}, "c:\\Users\\<USER>\\ONU\\Mirror.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "c:\\Users\\<USER>\\ONU\\Mirror.csproj", "projectName": "Mirror", "projectPath": "c:\\Users\\<USER>\\ONU\\Mirror.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "c:\\Users\\<USER>\\ONU\\Temp\\obj\\Mirror\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"c:\\Users\\<USER>\\ONU\\Mirror.CompilerSymbols.csproj": {"projectPath": "c:\\Users\\<USER>\\ONU\\Mirror.CompilerSymbols.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.205\\RuntimeIdentifierGraph.json"}}}}}