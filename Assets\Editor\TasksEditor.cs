using UnityEngine;
using UnityEditor;
using System.IO;

[CustomEditor(typeof(Tasks))]public class TasksEditor : Editor
{

    public override void OnInspectorGUI()
    {


        Tasks task = (Tasks)target;
        GUILayout.BeginHorizontal();
        GUILayout.Label($"Type:", GUILayout.Width(35));
        TaskType newTaskType = (TaskType)EditorGUILayout.EnumPopup(task.taskType, GUILayout.Width(80));

        if (newTaskType != task.taskType)
        {
            Undo.RecordObject(task, "Change Task Type");
            task.taskType = newTaskType;
            EditorUtility.SetDirty(task);
        }
        GUILayout.Space(10);
        BuildingManager buildingManager = FindFirstObjectByType<BuildingManager>();
        string[] buildingNames = new string[0];
        if (buildingManager != null)
        {
            var buildingDataList = buildingManager.GetAllBuildingData();
            buildingNames = new string[buildingDataList.Count];
            for (int i = 1; i < buildingDataList.Count; i++)
            {
                buildingNames[i] = buildingDataList[i].buildingName;
            }
        }

        GUILayout.Label("Building:", GUILayout.Width(60));
        int currentBuildingIndex = Mathf.Clamp(task.buildingIndex, 1, buildingNames.Length - 1);
        int newBuildingIndex = EditorGUILayout.Popup(currentBuildingIndex, buildingNames, GUILayout.Width(120));

        if (newBuildingIndex != task.buildingIndex)
        {
            Undo.RecordObject(task, "Change Task Building");
            task.buildingIndex = newBuildingIndex;
            EditorUtility.SetDirty(task);
        }
        GUILayout.EndHorizontal();

        if (GUILayout.Button("Complete Task"))
        {
            task.CompleteTask();
        }
        if (GUILayout.Button("Delete Task"))
        {
            DestroyImmediate(task.gameObject);
        }
        if (GUILayout.Button("Initialize Task"))
        {
            // Check for Tasks directory in Assets
            string tasksDirectoryPath = "Assets/Tasks";
            if (!Directory.Exists(tasksDirectoryPath))
            {
                Directory.CreateDirectory(tasksDirectoryPath);
                AssetDatabase.Refresh();
                Debug.Log("Created Tasks directory in Assets");
            }

            // Check for TaskType script
            string taskTypeScriptPath = "Assets/Tasks/" + task.taskType + ".cs";
            if (!File.Exists(taskTypeScriptPath))
            {
                CreateTaskTypeScript(taskTypeScriptPath, task.taskType.ToString());
                AssetDatabase.Refresh();
                Debug.Log("Created " + task.taskType + " script in Assets/Tasks");
            }
            // Create new task object
            GameObject newTaskObject = new GameObject(task.taskType.ToString());

            // Add the appropriate component based on task type (modular approach)
            string typeName = task.taskType.ToString();
            System.Type componentType = System.Type.GetType(typeName);

            // If not found in current assembly, search in all loaded assemblies
            if (componentType == null)
            {
                foreach (var assembly in System.AppDomain.CurrentDomain.GetAssemblies())
                {
                    componentType = assembly.GetType(typeName);
                    if (componentType != null)
                        break;
                }
            }

            if (componentType != null && componentType.IsSubclassOf(typeof(MonoBehaviour)))
            {
                newTaskObject.AddComponent(componentType);
            }
            else
            {
                Debug.LogError($"Could not find MonoBehaviour component type: {typeName}");
            }
            newTaskObject.transform.SetParent(task.transform);
        }
    }

    private void CreateTaskTypeScript(string scriptPath, string taskTypeName)
    {
        string scriptContent = @"using UnityEngine;

public class " + taskTypeName + @" : MonoBehaviour
{
    // Start is called before the first frame update
    void Start()
    {

    }

    // Update is called once per frame
    void Update()
    {

    }
}";

        File.WriteAllText(scriptPath, scriptContent);
    }
}