{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 36960, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 36960, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 36960, "tid": 131324, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 36960, "tid": 131324, "ts": 1754506450321340, "dur": 2241, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 36960, "tid": 131324, "ts": 1754506450329045, "dur": 795, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 36960, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 36960, "tid": 1, "ts": 1754506449078982, "dur": 4871, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 36960, "tid": 1, "ts": 1754506449083857, "dur": 47245, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 36960, "tid": 1, "ts": 1754506449131115, "dur": 102558, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 36960, "tid": 131324, "ts": 1754506450329844, "dur": 13, "ph": "X", "name": "", "args": {}}, {"pid": 36960, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449072099, "dur": 87, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449072187, "dur": 1236397, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449073012, "dur": 2553, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449075569, "dur": 1302, "ph": "X", "name": "ProcessMessages 20496", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449076876, "dur": 716, "ph": "X", "name": "ReadAsync 20496", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449077595, "dur": 12, "ph": "X", "name": "ProcessMessages 20496", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449077608, "dur": 245, "ph": "X", "name": "ReadAsync 20496", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449077858, "dur": 7, "ph": "X", "name": "ProcessMessages 7784", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449077866, "dur": 149, "ph": "X", "name": "ReadAsync 7784", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449078018, "dur": 3, "ph": "X", "name": "ProcessMessages 3239", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449078022, "dur": 18, "ph": "X", "name": "ReadAsync 3239", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449078050, "dur": 78, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449078129, "dur": 90, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449078220, "dur": 1, "ph": "X", "name": "ProcessMessages 1118", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449078222, "dur": 98, "ph": "X", "name": "ReadAsync 1118", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449078320, "dur": 1, "ph": "X", "name": "ProcessMessages 2395", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449078322, "dur": 22, "ph": "X", "name": "ReadAsync 2395", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449078346, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449078362, "dur": 13, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449078376, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449078405, "dur": 13, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449078420, "dur": 27, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449078448, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449078464, "dur": 13, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449078479, "dur": 16, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449078496, "dur": 12, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449078510, "dur": 14, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449078526, "dur": 20, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449078548, "dur": 12, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449078562, "dur": 18, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449078582, "dur": 20, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449078603, "dur": 16, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449078621, "dur": 12, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449078635, "dur": 14, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449078650, "dur": 13, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449078665, "dur": 12, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449078679, "dur": 15, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449078697, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449078711, "dur": 13, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449078726, "dur": 13, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449078740, "dur": 12, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449078755, "dur": 15, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449078771, "dur": 2, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449078774, "dur": 15, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449078791, "dur": 12, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449078806, "dur": 11, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449078819, "dur": 34, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449078856, "dur": 1, "ph": "X", "name": "ProcessMessages 178", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449078858, "dur": 19, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449078880, "dur": 16, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449078897, "dur": 14, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449078913, "dur": 16, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449078931, "dur": 12, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449078945, "dur": 11, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449078958, "dur": 16, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449078976, "dur": 16, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449078993, "dur": 15, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079010, "dur": 16, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079028, "dur": 20, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079049, "dur": 12, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079063, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079078, "dur": 14, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079094, "dur": 13, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079109, "dur": 14, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079125, "dur": 18, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079145, "dur": 14, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079161, "dur": 11, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079174, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079190, "dur": 15, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079208, "dur": 14, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079224, "dur": 13, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079239, "dur": 36, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079278, "dur": 14, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079293, "dur": 13, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079307, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079309, "dur": 37, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079348, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079365, "dur": 15, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079383, "dur": 26, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079410, "dur": 14, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079427, "dur": 13, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079441, "dur": 15, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079458, "dur": 16, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079476, "dur": 15, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079493, "dur": 15, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079510, "dur": 12, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079524, "dur": 12, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079538, "dur": 26, "ph": "X", "name": "ReadAsync 119", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079565, "dur": 13, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079580, "dur": 12, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079594, "dur": 14, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079609, "dur": 12, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079623, "dur": 58, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079685, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079688, "dur": 29, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079719, "dur": 1, "ph": "X", "name": "ProcessMessages 989", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079721, "dur": 23, "ph": "X", "name": "ReadAsync 989", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079746, "dur": 2, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079749, "dur": 18, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079770, "dur": 16, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079788, "dur": 13, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079802, "dur": 13, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079817, "dur": 14, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079832, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079847, "dur": 13, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079862, "dur": 13, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079876, "dur": 13, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079891, "dur": 15, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079907, "dur": 14, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079924, "dur": 12, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079937, "dur": 13, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079952, "dur": 14, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079967, "dur": 13, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449079982, "dur": 17, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449080001, "dur": 13, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449080015, "dur": 12, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449080029, "dur": 13, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449080044, "dur": 14, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449080060, "dur": 13, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449080075, "dur": 12, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449080089, "dur": 24, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449080115, "dur": 14, "ph": "X", "name": "ReadAsync 734", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449080130, "dur": 15, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449080147, "dur": 1, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449080149, "dur": 13, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449080164, "dur": 18, "ph": "X", "name": "ReadAsync 107", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449080183, "dur": 14, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449080199, "dur": 13, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449080214, "dur": 21, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449080236, "dur": 13, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449080251, "dur": 13, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449080267, "dur": 182, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449080450, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449080466, "dur": 195, "ph": "X", "name": "ReadAsync 122", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449080665, "dur": 68, "ph": "X", "name": "ReadAsync 99", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449080735, "dur": 2, "ph": "X", "name": "ProcessMessages 2356", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449080739, "dur": 28, "ph": "X", "name": "ReadAsync 2356", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449080769, "dur": 55, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449080826, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449080828, "dur": 100, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449080929, "dur": 1, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449080930, "dur": 54, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449080988, "dur": 2, "ph": "X", "name": "ProcessMessages 1663", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449080991, "dur": 68, "ph": "X", "name": "ReadAsync 1663", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449081063, "dur": 2, "ph": "X", "name": "ProcessMessages 1083", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449081066, "dur": 75, "ph": "X", "name": "ReadAsync 1083", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449081145, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449081147, "dur": 91, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449081240, "dur": 2, "ph": "X", "name": "ProcessMessages 1751", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449081243, "dur": 24, "ph": "X", "name": "ReadAsync 1751", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449081271, "dur": 71, "ph": "X", "name": "ReadAsync 944", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449081346, "dur": 62, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449081411, "dur": 1, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449081413, "dur": 29, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449081444, "dur": 1, "ph": "X", "name": "ProcessMessages 1361", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449081446, "dur": 121, "ph": "X", "name": "ReadAsync 1361", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449081571, "dur": 1, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449081573, "dur": 104, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449081681, "dur": 3, "ph": "X", "name": "ProcessMessages 2997", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449081686, "dur": 48, "ph": "X", "name": "ReadAsync 2997", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449081736, "dur": 1, "ph": "X", "name": "ProcessMessages 515", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449081738, "dur": 27, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449081769, "dur": 32, "ph": "X", "name": "ReadAsync 51", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449081805, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449081808, "dur": 35, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449081845, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449081847, "dur": 22, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449081873, "dur": 16, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449081891, "dur": 35, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449081928, "dur": 17, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449081947, "dur": 24, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449081973, "dur": 14, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449081989, "dur": 13, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449082005, "dur": 20, "ph": "X", "name": "ReadAsync 147", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449082028, "dur": 14, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449082044, "dur": 15, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449082062, "dur": 39, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449082103, "dur": 15, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449082120, "dur": 14, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449082136, "dur": 28, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449082166, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449082168, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449082185, "dur": 1, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449082187, "dur": 428, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449082617, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449082618, "dur": 98, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449082720, "dur": 7, "ph": "X", "name": "ProcessMessages 10854", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449082729, "dur": 69, "ph": "X", "name": "ReadAsync 10854", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449082800, "dur": 1, "ph": "X", "name": "ProcessMessages 1088", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449082802, "dur": 37, "ph": "X", "name": "ReadAsync 1088", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449082843, "dur": 1, "ph": "X", "name": "ProcessMessages 971", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449082846, "dur": 38, "ph": "X", "name": "ReadAsync 971", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449082886, "dur": 1, "ph": "X", "name": "ProcessMessages 1482", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449082890, "dur": 21, "ph": "X", "name": "ReadAsync 1482", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449082914, "dur": 14, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449082930, "dur": 18, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449082950, "dur": 13, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449082964, "dur": 17, "ph": "X", "name": "ReadAsync 137", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449082983, "dur": 13, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449082999, "dur": 15, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449083015, "dur": 13, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449083030, "dur": 9, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449083041, "dur": 15, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449083057, "dur": 13, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449083072, "dur": 20, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449083094, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449083111, "dur": 17, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449083129, "dur": 14, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449083145, "dur": 17, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449083163, "dur": 21, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449083186, "dur": 16, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449083204, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449083220, "dur": 13, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449083254, "dur": 83, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449083340, "dur": 1, "ph": "X", "name": "ProcessMessages 1156", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449083343, "dur": 36, "ph": "X", "name": "ReadAsync 1156", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449083381, "dur": 2, "ph": "X", "name": "ProcessMessages 1364", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449083384, "dur": 25, "ph": "X", "name": "ReadAsync 1364", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449083412, "dur": 1, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449083414, "dur": 28, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449083445, "dur": 14, "ph": "X", "name": "ReadAsync 826", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449083461, "dur": 15, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449083478, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449083495, "dur": 19, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449083516, "dur": 14, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449083532, "dur": 17, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449083550, "dur": 14, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449083567, "dur": 17, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449083585, "dur": 14, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449083601, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449083625, "dur": 19, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449083646, "dur": 16, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449083664, "dur": 42, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449083709, "dur": 1, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449083711, "dur": 17, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449083730, "dur": 1, "ph": "X", "name": "ProcessMessages 73", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449083732, "dur": 45, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449083780, "dur": 21, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449083802, "dur": 1, "ph": "X", "name": "ProcessMessages 698", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449083804, "dur": 16, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449083821, "dur": 16, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449083839, "dur": 16, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449083857, "dur": 14, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449083873, "dur": 55, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449083931, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449083935, "dur": 34, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449083972, "dur": 2, "ph": "X", "name": "ProcessMessages 1933", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449083975, "dur": 35, "ph": "X", "name": "ReadAsync 1933", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449084012, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449084013, "dur": 24, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449084039, "dur": 26, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449084067, "dur": 26, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449084095, "dur": 20, "ph": "X", "name": "ReadAsync 818", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449084117, "dur": 24, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449084143, "dur": 33, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449084179, "dur": 21, "ph": "X", "name": "ReadAsync 1094", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449084203, "dur": 18, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449084223, "dur": 20, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449084245, "dur": 20, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449084267, "dur": 20, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449084290, "dur": 29, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449084321, "dur": 21, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449084344, "dur": 20, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449084366, "dur": 20, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449084388, "dur": 20, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449084410, "dur": 19, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449084431, "dur": 142, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449084575, "dur": 41, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449084617, "dur": 2, "ph": "X", "name": "ProcessMessages 3716", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449084620, "dur": 14, "ph": "X", "name": "ReadAsync 3716", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449084636, "dur": 13, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449084651, "dur": 52, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449084705, "dur": 27, "ph": "X", "name": "ReadAsync 706", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449084735, "dur": 2, "ph": "X", "name": "ProcessMessages 925", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449084738, "dur": 28, "ph": "X", "name": "ReadAsync 925", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449084769, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449084772, "dur": 26, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449084799, "dur": 1, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449084801, "dur": 18, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449084822, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449084823, "dur": 20, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449084846, "dur": 20, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449084867, "dur": 29, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449084898, "dur": 151, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449085051, "dur": 2, "ph": "X", "name": "ProcessMessages 3202", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449085053, "dur": 15, "ph": "X", "name": "ReadAsync 3202", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449085070, "dur": 16, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449085088, "dur": 14, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449085103, "dur": 15, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449085120, "dur": 13, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449085135, "dur": 19, "ph": "X", "name": "ReadAsync 99", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449085157, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449085179, "dur": 13, "ph": "X", "name": "ReadAsync 748", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449085194, "dur": 26, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449085222, "dur": 16, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449085240, "dur": 14, "ph": "X", "name": "ReadAsync 767", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449085256, "dur": 15, "ph": "X", "name": "ReadAsync 102", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449085273, "dur": 13, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449085288, "dur": 135, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449085424, "dur": 1, "ph": "X", "name": "ProcessMessages 2180", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449085426, "dur": 71, "ph": "X", "name": "ReadAsync 2180", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449085498, "dur": 1, "ph": "X", "name": "ProcessMessages 1405", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449085499, "dur": 152, "ph": "X", "name": "ReadAsync 1405", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449085653, "dur": 1, "ph": "X", "name": "ProcessMessages 1618", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449085654, "dur": 71, "ph": "X", "name": "ReadAsync 1618", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449085729, "dur": 2, "ph": "X", "name": "ProcessMessages 2808", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449085732, "dur": 36, "ph": "X", "name": "ReadAsync 2808", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449085769, "dur": 1, "ph": "X", "name": "ProcessMessages 2030", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449085771, "dur": 14, "ph": "X", "name": "ReadAsync 2030", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449085787, "dur": 13, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449085802, "dur": 14, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449085818, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449085834, "dur": 15, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449085851, "dur": 14, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449085867, "dur": 14, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449085883, "dur": 14, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449085899, "dur": 13, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449085914, "dur": 13, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449085929, "dur": 14, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449085945, "dur": 15, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449085962, "dur": 13, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449085977, "dur": 14, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449085992, "dur": 17, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449086011, "dur": 13, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449086026, "dur": 13, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449086041, "dur": 33, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449086076, "dur": 30, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449086109, "dur": 1, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449086111, "dur": 25, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449086138, "dur": 1, "ph": "X", "name": "ProcessMessages 795", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449086139, "dur": 17, "ph": "X", "name": "ReadAsync 795", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449086158, "dur": 14, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449086174, "dur": 15, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449086191, "dur": 13, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449086206, "dur": 56, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449086265, "dur": 1, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449086267, "dur": 33, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449086302, "dur": 1, "ph": "X", "name": "ProcessMessages 1455", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449086304, "dur": 32, "ph": "X", "name": "ReadAsync 1455", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449086339, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449086341, "dur": 21, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449086364, "dur": 21, "ph": "X", "name": "ReadAsync 834", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449086391, "dur": 192, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449086585, "dur": 3, "ph": "X", "name": "ProcessMessages 4723", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449086590, "dur": 38, "ph": "X", "name": "ReadAsync 4723", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449086631, "dur": 14, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449086647, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449086649, "dur": 61, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449086713, "dur": 23, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449086739, "dur": 23, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449086764, "dur": 23, "ph": "X", "name": "ReadAsync 747", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449086789, "dur": 23, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449086815, "dur": 85, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449086901, "dur": 1, "ph": "X", "name": "ProcessMessages 1254", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449086902, "dur": 13, "ph": "X", "name": "ReadAsync 1254", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449086918, "dur": 18, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449086937, "dur": 12, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449086952, "dur": 16, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449086969, "dur": 19, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449086990, "dur": 14, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449087006, "dur": 16, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449087025, "dur": 15, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449087042, "dur": 20, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449087063, "dur": 13, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449087077, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449087093, "dur": 22, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449087118, "dur": 14, "ph": "X", "name": "ReadAsync 686", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449087134, "dur": 16, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449087152, "dur": 13, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449087168, "dur": 19, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449087191, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449087257, "dur": 1, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449087259, "dur": 34, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449087295, "dur": 1, "ph": "X", "name": "ProcessMessages 1552", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449087297, "dur": 60, "ph": "X", "name": "ReadAsync 1552", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449087359, "dur": 12, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449087373, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449087393, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449087408, "dur": 13, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449087423, "dur": 14, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449087439, "dur": 20, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449087461, "dur": 12, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449087475, "dur": 12, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449087489, "dur": 12, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449087503, "dur": 14, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449087518, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449087534, "dur": 13, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449087549, "dur": 54, "ph": "X", "name": "ReadAsync 135", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449087605, "dur": 26, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449087632, "dur": 1, "ph": "X", "name": "ProcessMessages 1286", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449087634, "dur": 13, "ph": "X", "name": "ReadAsync 1286", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449087648, "dur": 15, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449087665, "dur": 13, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449087680, "dur": 13, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449087695, "dur": 14, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449087711, "dur": 33, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449087746, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449087748, "dur": 16, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449087767, "dur": 18, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449087788, "dur": 18, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449087807, "dur": 29, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449087838, "dur": 13, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449087853, "dur": 16, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449087871, "dur": 13, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449087886, "dur": 11, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449087898, "dur": 15, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449087916, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449087931, "dur": 41, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449087973, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449087989, "dur": 21, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449088012, "dur": 20, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449088034, "dur": 77, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449088113, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449088135, "dur": 13, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449088149, "dur": 13, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449088164, "dur": 17, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449088183, "dur": 16, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449088200, "dur": 15, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449088216, "dur": 2, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449088218, "dur": 12, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449088233, "dur": 12, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449088246, "dur": 1, "ph": "X", "name": "ProcessMessages 93", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449088248, "dur": 13, "ph": "X", "name": "ReadAsync 93", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449088262, "dur": 58, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449088322, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449088347, "dur": 18, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449088367, "dur": 13, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449088382, "dur": 62, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449088445, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449088469, "dur": 18, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449088490, "dur": 13, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449088505, "dur": 13, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449088520, "dur": 13, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449088534, "dur": 1, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449088536, "dur": 16, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449088554, "dur": 12, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449088568, "dur": 30, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449088600, "dur": 62, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449088664, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449088679, "dur": 13, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449088695, "dur": 14, "ph": "X", "name": "ReadAsync 105", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449088710, "dur": 1, "ph": "X", "name": "ProcessMessages 409", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449088712, "dur": 24, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449088738, "dur": 16, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449088756, "dur": 17, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449088775, "dur": 13, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449088791, "dur": 12, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449088805, "dur": 12, "ph": "X", "name": "ReadAsync 93", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449088819, "dur": 62, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449088883, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449088899, "dur": 14, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449088915, "dur": 14, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449088930, "dur": 13, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449088945, "dur": 54, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449089000, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449089001, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449089023, "dur": 14, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449089039, "dur": 15, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449089056, "dur": 62, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449089121, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449089138, "dur": 17, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449089159, "dur": 13, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449089174, "dur": 105, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449089282, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449089308, "dur": 1, "ph": "X", "name": "ProcessMessages 1026", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449089310, "dur": 28, "ph": "X", "name": "ReadAsync 1026", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449089341, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449089363, "dur": 13, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449089379, "dur": 16, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449089398, "dur": 64, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449089464, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449089480, "dur": 18, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449089500, "dur": 17, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449089520, "dur": 17, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449089538, "dur": 15, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449089555, "dur": 14, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449089572, "dur": 15, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449089589, "dur": 12, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449089602, "dur": 12, "ph": "X", "name": "ReadAsync 86", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449089616, "dur": 13, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449089630, "dur": 53, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449089685, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449089699, "dur": 16, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449089716, "dur": 1, "ph": "X", "name": "ProcessMessages 221", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449089718, "dur": 13, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449089733, "dur": 13, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449089749, "dur": 54, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449089806, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449089825, "dur": 14, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449089840, "dur": 13, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449089855, "dur": 58, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449089916, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449089933, "dur": 13, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449089948, "dur": 14, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449089964, "dur": 12, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449089978, "dur": 53, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449090031, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449090034, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449090052, "dur": 23, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449090077, "dur": 13, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449090092, "dur": 59, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449090154, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449090169, "dur": 22, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449090193, "dur": 14, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449090208, "dur": 59, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449090271, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449090290, "dur": 1, "ph": "X", "name": "ProcessMessages 571", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449090292, "dur": 64, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449090359, "dur": 1, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449090362, "dur": 37, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449090401, "dur": 16, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449090419, "dur": 26, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449090448, "dur": 57, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449090507, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449090522, "dur": 24, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449090548, "dur": 37, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449090587, "dur": 47, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449090637, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449090640, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449090667, "dur": 1, "ph": "X", "name": "ProcessMessages 760", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449090669, "dur": 55, "ph": "X", "name": "ReadAsync 760", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449090733, "dur": 4, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449090741, "dur": 36, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449090782, "dur": 27, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449090811, "dur": 1, "ph": "X", "name": "ProcessMessages 944", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449090814, "dur": 23, "ph": "X", "name": "ReadAsync 944", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449090839, "dur": 55, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449090899, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449090945, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449090947, "dur": 33, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449090982, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449090984, "dur": 42, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449091030, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449091075, "dur": 1, "ph": "X", "name": "ProcessMessages 807", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449091079, "dur": 34, "ph": "X", "name": "ReadAsync 807", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449091117, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449091119, "dur": 67, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449091190, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449091222, "dur": 2, "ph": "X", "name": "ProcessMessages 431", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449091226, "dur": 77, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449091307, "dur": 1, "ph": "X", "name": "ProcessMessages 724", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449091309, "dur": 36, "ph": "X", "name": "ReadAsync 724", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449091349, "dur": 1, "ph": "X", "name": "ProcessMessages 425", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449091352, "dur": 60, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449091415, "dur": 2, "ph": "X", "name": "ProcessMessages 878", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449091418, "dur": 63, "ph": "X", "name": "ReadAsync 878", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449091485, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449091524, "dur": 2, "ph": "X", "name": "ProcessMessages 1245", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449091527, "dur": 51, "ph": "X", "name": "ReadAsync 1245", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449091582, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449091583, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449091631, "dur": 1, "ph": "X", "name": "ProcessMessages 957", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449091634, "dur": 29, "ph": "X", "name": "ReadAsync 957", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449091665, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449091667, "dur": 83, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449091753, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449091755, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449091827, "dur": 2, "ph": "X", "name": "ProcessMessages 1096", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449091831, "dur": 41, "ph": "X", "name": "ReadAsync 1096", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449091876, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449091912, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449091914, "dur": 35, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449091952, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449091955, "dur": 45, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449092004, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449092036, "dur": 1, "ph": "X", "name": "ProcessMessages 793", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449092038, "dur": 87, "ph": "X", "name": "ReadAsync 793", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449092129, "dur": 1, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449092131, "dur": 44, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449092178, "dur": 90, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449092272, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449092274, "dur": 31, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449092307, "dur": 83, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449092393, "dur": 1, "ph": "X", "name": "ProcessMessages 509", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449092396, "dur": 38, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449092437, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449092440, "dur": 48, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449092491, "dur": 1, "ph": "X", "name": "ProcessMessages 911", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449092493, "dur": 88, "ph": "X", "name": "ReadAsync 911", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449092583, "dur": 91, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449092675, "dur": 1, "ph": "X", "name": "ProcessMessages 1499", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449092677, "dur": 20, "ph": "X", "name": "ReadAsync 1499", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449092698, "dur": 13, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449092715, "dur": 15, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449092731, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449092733, "dur": 18, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449092754, "dur": 17, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449092773, "dur": 21, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449092796, "dur": 12, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449092810, "dur": 14, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449092845, "dur": 12, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449092859, "dur": 25, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449092886, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449092903, "dur": 14, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449092919, "dur": 16, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449092936, "dur": 13, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449092951, "dur": 62, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449093015, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449093039, "dur": 22, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449093064, "dur": 14, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449093080, "dur": 23, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449093105, "dur": 22, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449093129, "dur": 14, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449093145, "dur": 13, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449093160, "dur": 13, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449093174, "dur": 55, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449093231, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449093316, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449093318, "dur": 25, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449093345, "dur": 1, "ph": "X", "name": "ProcessMessages 733", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449093347, "dur": 12, "ph": "X", "name": "ReadAsync 733", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449093362, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449093383, "dur": 1, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449093385, "dur": 17, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449093405, "dur": 14, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449093421, "dur": 13, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449093436, "dur": 18, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449093456, "dur": 18, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449093477, "dur": 14, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449093493, "dur": 15, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449093511, "dur": 23, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449093535, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449093578, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449093594, "dur": 12, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449093608, "dur": 14, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449093624, "dur": 12, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449093638, "dur": 80, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449093721, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449093739, "dur": 13, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449093754, "dur": 16, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449093772, "dur": 63, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449093838, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449093853, "dur": 14, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449093869, "dur": 15, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449093886, "dur": 14, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449093902, "dur": 59, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449093963, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449093979, "dur": 13, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449093993, "dur": 14, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449094009, "dur": 12, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449094024, "dur": 61, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449094087, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449094108, "dur": 16, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449094126, "dur": 14, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449094142, "dur": 59, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449094202, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449094220, "dur": 13, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449094235, "dur": 27, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449094264, "dur": 55, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449094321, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449094338, "dur": 76, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449094416, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449094432, "dur": 14, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449094448, "dur": 157, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449094614, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449094616, "dur": 41, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449094662, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449094708, "dur": 1, "ph": "X", "name": "ProcessMessages 940", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449094711, "dur": 26, "ph": "X", "name": "ReadAsync 940", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449094738, "dur": 1, "ph": "X", "name": "ProcessMessages 824", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449094740, "dur": 28, "ph": "X", "name": "ReadAsync 824", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449094769, "dur": 1, "ph": "X", "name": "ProcessMessages 450", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449094771, "dur": 22, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449094796, "dur": 20, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449094818, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449094840, "dur": 80, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449094921, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449094939, "dur": 14, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449094954, "dur": 13, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449094969, "dur": 31, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449095003, "dur": 1, "ph": "X", "name": "ProcessMessages 69", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449095005, "dur": 16, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449095023, "dur": 57, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449095082, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449095105, "dur": 15, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449095123, "dur": 13, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449095137, "dur": 60, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449095199, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449095218, "dur": 20, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449095240, "dur": 15, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449095257, "dur": 16, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449095275, "dur": 16, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449095293, "dur": 15, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449095309, "dur": 14, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449095325, "dur": 16, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449095343, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449095359, "dur": 56, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449095417, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449095433, "dur": 15, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449095450, "dur": 16, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449095469, "dur": 16, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449095486, "dur": 20, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449095508, "dur": 21, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449095531, "dur": 55, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449095588, "dur": 60, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449095650, "dur": 24, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449095676, "dur": 21, "ph": "X", "name": "ReadAsync 843", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449095698, "dur": 54, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449095754, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449095776, "dur": 31, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449095809, "dur": 17, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449095828, "dur": 14, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449095844, "dur": 59, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449095904, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449095921, "dur": 15, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449095938, "dur": 15, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449095954, "dur": 15, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449095972, "dur": 14, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449095988, "dur": 60, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449096049, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449096066, "dur": 16, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449096085, "dur": 15, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449096101, "dur": 57, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449096160, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449096176, "dur": 14, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449096192, "dur": 34, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449096228, "dur": 90, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449096321, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449096353, "dur": 1, "ph": "X", "name": "ProcessMessages 1167", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449096355, "dur": 26, "ph": "X", "name": "ReadAsync 1167", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449096382, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449096407, "dur": 15, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449096424, "dur": 17, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449096442, "dur": 59, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449096505, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449096541, "dur": 1, "ph": "X", "name": "ProcessMessages 727", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449096543, "dur": 31, "ph": "X", "name": "ReadAsync 727", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449096576, "dur": 1, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449096578, "dur": 26, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449096606, "dur": 20, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449096628, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449096655, "dur": 20, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449096676, "dur": 58, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449096738, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449096774, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449096776, "dur": 25, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449096804, "dur": 1, "ph": "X", "name": "ProcessMessages 791", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449096806, "dur": 25, "ph": "X", "name": "ReadAsync 791", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449096833, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449096835, "dur": 30, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449096867, "dur": 1, "ph": "X", "name": "ProcessMessages 381", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449096869, "dur": 36, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449096907, "dur": 1, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449096909, "dur": 46, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449096958, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449096977, "dur": 20, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449096999, "dur": 15, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449097015, "dur": 14, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449097032, "dur": 17, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449097050, "dur": 15, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449097068, "dur": 14, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449097084, "dur": 13, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449097099, "dur": 13, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449097114, "dur": 58, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449097174, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449097189, "dur": 15, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449097205, "dur": 15, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449097223, "dur": 14, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449097238, "dur": 54, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449097294, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449097318, "dur": 14, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449097334, "dur": 14, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449097349, "dur": 57, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449097408, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449097459, "dur": 18, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449097479, "dur": 16, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449097497, "dur": 17, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449097516, "dur": 14, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449097532, "dur": 15, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449097548, "dur": 13, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449097563, "dur": 14, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449097578, "dur": 14, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449097593, "dur": 54, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449097649, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449097670, "dur": 15, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449097687, "dur": 19, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449097708, "dur": 13, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449097723, "dur": 51, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449097778, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449097805, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449097807, "dur": 26, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449097835, "dur": 1, "ph": "X", "name": "ProcessMessages 663", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449097837, "dur": 45, "ph": "X", "name": "ReadAsync 663", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449097884, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449097903, "dur": 22, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449097927, "dur": 15, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449097944, "dur": 14, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449097960, "dur": 53, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449098014, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449098037, "dur": 16, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449098055, "dur": 13, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449098070, "dur": 55, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449098127, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449098143, "dur": 15, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449098160, "dur": 18, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449098179, "dur": 13, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449098194, "dur": 51, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449098247, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449098267, "dur": 16, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449098285, "dur": 36, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449098324, "dur": 14, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449098340, "dur": 50, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449098392, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449098408, "dur": 14, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449098424, "dur": 18, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449098444, "dur": 58, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449098504, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449098520, "dur": 20, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449098542, "dur": 13, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449098558, "dur": 57, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449098616, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449098631, "dur": 25, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449098658, "dur": 25, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449098685, "dur": 16, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449098703, "dur": 15, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449098720, "dur": 18, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449098740, "dur": 13, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449098755, "dur": 14, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449098772, "dur": 56, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449098830, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449098847, "dur": 16, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449098865, "dur": 15, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449098882, "dur": 15, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449098899, "dur": 13, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449098914, "dur": 15, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449098931, "dur": 19, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449098952, "dur": 13, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449098967, "dur": 14, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449098983, "dur": 12, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449098997, "dur": 53, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449099051, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449099069, "dur": 16, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449099086, "dur": 26, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449099115, "dur": 13, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449099130, "dur": 51, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449099183, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449099204, "dur": 22, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449099229, "dur": 13, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449099244, "dur": 80, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449099327, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449099329, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449099375, "dur": 1, "ph": "X", "name": "ProcessMessages 939", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449099377, "dur": 33, "ph": "X", "name": "ReadAsync 939", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449099414, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449099416, "dur": 66, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449099484, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449099487, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449099530, "dur": 1, "ph": "X", "name": "ProcessMessages 811", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449099532, "dur": 31, "ph": "X", "name": "ReadAsync 811", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449099565, "dur": 1, "ph": "X", "name": "ProcessMessages 381", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449099568, "dur": 54, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449099626, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449099628, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449099672, "dur": 1, "ph": "X", "name": "ProcessMessages 1222", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449099676, "dur": 29, "ph": "X", "name": "ReadAsync 1222", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449099708, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449099711, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449099749, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449099788, "dur": 2, "ph": "X", "name": "ProcessMessages 969", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449099792, "dur": 22, "ph": "X", "name": "ReadAsync 969", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449099816, "dur": 38, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449099857, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449099879, "dur": 14, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449099895, "dur": 23, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449099919, "dur": 1, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449099922, "dur": 14, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449099939, "dur": 51, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449099991, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449099992, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449100012, "dur": 15, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449100030, "dur": 17, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449100048, "dur": 1, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449100051, "dur": 56, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449100109, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449100130, "dur": 21, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449100153, "dur": 14, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449100170, "dur": 52, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449100223, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449100252, "dur": 116, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449100372, "dur": 1, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449100375, "dur": 25, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449100402, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449100405, "dur": 21, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449100432, "dur": 164, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449100601, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449100665, "dur": 3, "ph": "X", "name": "ProcessMessages 2545", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449100669, "dur": 31, "ph": "X", "name": "ReadAsync 2545", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449100705, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449100785, "dur": 1, "ph": "X", "name": "ProcessMessages 922", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449100788, "dur": 42, "ph": "X", "name": "ReadAsync 922", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449100833, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449100836, "dur": 32, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449100873, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449100875, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449100923, "dur": 2, "ph": "X", "name": "ProcessMessages 982", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449100926, "dur": 42, "ph": "X", "name": "ReadAsync 982", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449100972, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449100974, "dur": 46, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449101022, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449101024, "dur": 43, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449101070, "dur": 3, "ph": "X", "name": "ProcessMessages 1256", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449101074, "dur": 45, "ph": "X", "name": "ReadAsync 1256", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449101123, "dur": 2, "ph": "X", "name": "ProcessMessages 950", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449101127, "dur": 37, "ph": "X", "name": "ReadAsync 950", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449101167, "dur": 1, "ph": "X", "name": "ProcessMessages 155", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449101170, "dur": 31, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449101203, "dur": 1, "ph": "X", "name": "ProcessMessages 181", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449101205, "dur": 44, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449101252, "dur": 1, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449101256, "dur": 144, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449101403, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449101406, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449101457, "dur": 1, "ph": "X", "name": "ProcessMessages 1167", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449101461, "dur": 40, "ph": "X", "name": "ReadAsync 1167", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449101503, "dur": 1, "ph": "X", "name": "ProcessMessages 843", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449101506, "dur": 38, "ph": "X", "name": "ReadAsync 843", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449101547, "dur": 1, "ph": "X", "name": "ProcessMessages 652", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449101549, "dur": 30, "ph": "X", "name": "ReadAsync 652", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449101582, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449101585, "dur": 72, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449101662, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449101716, "dur": 1, "ph": "X", "name": "ProcessMessages 1083", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449101719, "dur": 32, "ph": "X", "name": "ReadAsync 1083", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449101754, "dur": 1, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449101756, "dur": 30, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449101790, "dur": 1, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449101794, "dur": 40, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449101838, "dur": 1, "ph": "X", "name": "ProcessMessages 579", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449101841, "dur": 23, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449101867, "dur": 43, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449101915, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449101932, "dur": 1, "ph": "X", "name": "ProcessMessages 479", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449101934, "dur": 12, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449101949, "dur": 24, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449101974, "dur": 1, "ph": "X", "name": "ProcessMessages 642", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449101977, "dur": 23, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449102001, "dur": 23, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449102027, "dur": 14, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449102045, "dur": 14, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449102062, "dur": 70, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449102134, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449102154, "dur": 142, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449102301, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449102303, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449102337, "dur": 355, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449102695, "dur": 46, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449102744, "dur": 4, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449102750, "dur": 28, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449102780, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449102806, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449102808, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449102840, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449102864, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449102883, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449102908, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449102910, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449102940, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449102942, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449102965, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449102985, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449103014, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449103017, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449103048, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449103050, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449103075, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449103093, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449103115, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449103117, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449103145, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449103147, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449103169, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449103171, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449103190, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449103215, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449103217, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449103245, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449103247, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449103271, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449103298, "dur": 49, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449103351, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449103354, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449103394, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449103437, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449103464, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449103467, "dur": 29, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449103499, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449103501, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449103526, "dur": 56, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449103583, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449103586, "dur": 26, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449103616, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449103619, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449103662, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449103664, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449103697, "dur": 19, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449103719, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449103750, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449103752, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449103787, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449103789, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449103811, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449103830, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449103854, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449103857, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449103886, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449103888, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449103914, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449103937, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449103962, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449103967, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449103999, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449104001, "dur": 22, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449104026, "dur": 17, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449104045, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449104070, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449104073, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449104102, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449104104, "dur": 25, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449104132, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449104153, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449104179, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449104181, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449104211, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449104213, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449104240, "dur": 16, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449104258, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449104293, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449104296, "dur": 41, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449104340, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449104343, "dur": 32, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449104376, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449104378, "dur": 32, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449104412, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449104434, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449104463, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449104465, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449104499, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449104502, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449104532, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449104534, "dur": 26, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449104563, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449104565, "dur": 43, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449104614, "dur": 2, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449104618, "dur": 28, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449104650, "dur": 17, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449104669, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449104699, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449104701, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449104729, "dur": 23, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449104755, "dur": 31, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449104790, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449104793, "dur": 32, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449104828, "dur": 21, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449104851, "dur": 25, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449104879, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449104882, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449104915, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449104918, "dur": 21, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449104942, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449104965, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105004, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105008, "dur": 32, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105043, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105046, "dur": 30, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105079, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105081, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105106, "dur": 24, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105132, "dur": 25, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105161, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105164, "dur": 29, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105197, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105200, "dur": 24, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105227, "dur": 21, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105250, "dur": 38, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105292, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105295, "dur": 30, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105328, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105331, "dur": 31, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105365, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105368, "dur": 24, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105396, "dur": 18, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105416, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105449, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105451, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105484, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105486, "dur": 25, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105513, "dur": 20, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105535, "dur": 24, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105562, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105565, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105594, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105596, "dur": 19, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105618, "dur": 25, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105646, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105649, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105681, "dur": 2, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105685, "dur": 23, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105711, "dur": 21, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105732, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105734, "dur": 26, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105763, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105766, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105796, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105798, "dur": 23, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105822, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105824, "dur": 23, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105850, "dur": 27, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105880, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105882, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105915, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105918, "dur": 25, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105947, "dur": 17, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449105966, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449106002, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449106005, "dur": 31, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449106038, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449106042, "dur": 24, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449106067, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449106069, "dur": 17, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449106088, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449106117, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449106120, "dur": 142, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449106336, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449106340, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449106374, "dur": 1, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449106377, "dur": 21, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449106398, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449106402, "dur": 23, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449106429, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449106432, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449106463, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449106466, "dur": 18, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449106486, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449106530, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449106562, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449106564, "dur": 22, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449106589, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449106608, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449106610, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449106637, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449106639, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449106670, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449106673, "dur": 16, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449106691, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449106693, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449106715, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449106744, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449106748, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449106778, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449106781, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449106810, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449106811, "dur": 19, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449106833, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449106870, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449106872, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449106905, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449106908, "dur": 22, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449106933, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449106954, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449106983, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449106985, "dur": 21, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449107008, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449107010, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449107030, "dur": 21, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449107053, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449107056, "dur": 25, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449107085, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449107115, "dur": 17, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449107135, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449107165, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449107168, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449107198, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449107201, "dur": 25, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449107229, "dur": 19, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449107250, "dur": 21, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449107274, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449107276, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449107311, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449107314, "dur": 26, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449107343, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449107363, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449107388, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449107391, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449107424, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449107426, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449107451, "dur": 26, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449107479, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449107510, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449107514, "dur": 44, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449107561, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449107564, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449107593, "dur": 24, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449107618, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449107645, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449107647, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449107678, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449107680, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449107704, "dur": 18, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449107724, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449107748, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449107750, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449107775, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449107799, "dur": 25, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449107828, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449107831, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449107857, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449107881, "dur": 1654, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449109542, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449109545, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449109579, "dur": 136, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449109718, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449109721, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449109747, "dur": 81, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449109833, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449109861, "dur": 4906, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449114776, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449114780, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449114842, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449114846, "dur": 1848, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449116700, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449116702, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449116747, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449116750, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449116791, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449116793, "dur": 247, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449117044, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449117046, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449117084, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449117087, "dur": 8441, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449125538, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449125542, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449125584, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449125605, "dur": 121, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449125732, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449125734, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449125771, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449125773, "dur": 368, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449126145, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449126218, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449126220, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449126261, "dur": 115, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449126382, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449126424, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449126427, "dur": 131, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449126560, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449126562, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449126594, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449126597, "dur": 55, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449126654, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449126680, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449126684, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449126710, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449126736, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449126738, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449126770, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449126797, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449126799, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449126857, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449126881, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449126883, "dur": 80, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449126966, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449126994, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449126996, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449127019, "dur": 66, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449127090, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449127121, "dur": 175, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449127300, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449127320, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449127354, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449127387, "dur": 97, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449127488, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449127511, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449127515, "dur": 63, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449127585, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449127612, "dur": 23, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449127638, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449127640, "dur": 105, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449127747, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449127787, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449127790, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449127835, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449127868, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449127870, "dur": 81, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449127953, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449127987, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449127990, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449128020, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449128044, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449128046, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449128069, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449128102, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449128105, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449128136, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449128165, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449128167, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449128197, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449128221, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449128223, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449128245, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449128283, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449128306, "dur": 209, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449128519, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449128550, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449128576, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449128580, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449128628, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449128657, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449128661, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449128686, "dur": 304, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449128994, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449128996, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449129036, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449129040, "dur": 39, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449129082, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449129084, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449129136, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449129138, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449129171, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449129174, "dur": 46, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449129222, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449129225, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449129256, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449129258, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449129287, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449129290, "dur": 181, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449129474, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449129476, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449129502, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449129505, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449129536, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449129537, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449129559, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449129561, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449129590, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449129613, "dur": 24, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449129640, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449129642, "dur": 25, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449129669, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449129693, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449129695, "dur": 51, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449129748, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449129774, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449129776, "dur": 76, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449129855, "dur": 91, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449129951, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449129976, "dur": 28, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449130008, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449130030, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449130070, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449130090, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449130122, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449130146, "dur": 34, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449130188, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449130216, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449130218, "dur": 24, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449130245, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449130265, "dur": 59, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449130328, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449130354, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449130398, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449130416, "dur": 81, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449130501, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449130537, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449130539, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449130565, "dur": 170, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449130741, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449130770, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449130772, "dur": 85, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449130859, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449130887, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449130889, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449130914, "dur": 144, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449131060, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449131088, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449131091, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449131123, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449131159, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449131178, "dur": 238, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449131421, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449131423, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449131450, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449131452, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449131479, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449131511, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449131514, "dur": 69, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449131586, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449131613, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449131615, "dur": 88, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449131706, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449131730, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449131732, "dur": 68, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449131804, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449131832, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449131887, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449131916, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449131918, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449131951, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449131976, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449132002, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449132031, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449132033, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449132060, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449132062, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449132085, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449132087, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449132115, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449132150, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449132153, "dur": 96, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449132254, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449132280, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449132282, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449132303, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449132305, "dur": 98, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449132407, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449132409, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449132439, "dur": 162, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449132603, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449132637, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449132639, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449132670, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449132672, "dur": 133, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449132808, "dur": 95, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449132905, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449132907, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449132943, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449132945, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449132983, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449133009, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449133011, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449133034, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449133101, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449133103, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449133140, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449133142, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449133169, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449133171, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449133212, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449133236, "dur": 23, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449133263, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449133265, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449133294, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449133296, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449133322, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449133349, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449133374, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449133376, "dur": 121, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449133502, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449133525, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449133527, "dur": 66, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449133595, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449133620, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449133622, "dur": 81, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449133707, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449133710, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449133788, "dur": 156, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449133948, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449133950, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449133989, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449133991, "dur": 218, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449134214, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449134216, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449134265, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449134267, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449134299, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449134338, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449134341, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449134376, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449134378, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449134414, "dur": 111, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449134530, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449134558, "dur": 155, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449134716, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449134718, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449134752, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449134753, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449134796, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449134798, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449134825, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449134860, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449134888, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449134891, "dur": 130, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449135025, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449135048, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449135079, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449135081, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449135129, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449135156, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449135184, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449135210, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449135212, "dur": 55, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449135271, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449135292, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449135311, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449135347, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449135370, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449135420, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449135444, "dur": 712, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449136161, "dur": 56, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449136221, "dur": 3, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449136225, "dur": 27, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449136256, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449136278, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449136304, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449136306, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449136359, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449136362, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449136393, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449136395, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449136417, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449136461, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449136498, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449136545, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449136585, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449136587, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449136627, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449136629, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449136654, "dur": 122, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449136781, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449136825, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449136828, "dur": 227, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449137060, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449137062, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449137106, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449137108, "dur": 142, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449137254, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449137295, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449137297, "dur": 33, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449137333, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449137336, "dur": 78, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449137418, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449137422, "dur": 80, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449137506, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449137508, "dur": 113, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449137625, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449137629, "dur": 116, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449137749, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449137753, "dur": 74, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449137831, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449137833, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449137876, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449137878, "dur": 221, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449138104, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449138108, "dur": 174, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449138286, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449138288, "dur": 160, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449138452, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449138454, "dur": 116, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449138575, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449138578, "dur": 87, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449138674, "dur": 4, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449138681, "dur": 124, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449138811, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449138818, "dur": 161, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449138987, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449138992, "dur": 171, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449139167, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449139169, "dur": 188, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449139362, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449139421, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449139424, "dur": 212, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449139639, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449139642, "dur": 136, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449139782, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449139784, "dur": 76, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449139862, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449139864, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449139894, "dur": 294, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449140193, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449140207, "dur": 590, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449140802, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449140839, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449140843, "dur": 76866, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449217718, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449217723, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449217792, "dur": 2184, "ph": "X", "name": "ProcessMessages 782", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449219981, "dur": 59, "ph": "X", "name": "ReadAsync 782", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449220045, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449220049, "dur": 53, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449220105, "dur": 110, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449220220, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449220223, "dur": 311, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449220536, "dur": 98, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449220639, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449220642, "dur": 489, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449221135, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449221177, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449221180, "dur": 335, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449221519, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449221521, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449221556, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449221558, "dur": 165, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449221726, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449221727, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449221763, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449221766, "dur": 104, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449221872, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449221874, "dur": 207, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449222086, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449222088, "dur": 181, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449222273, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449222275, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449222314, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449222316, "dur": 450, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449222771, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449222806, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449222809, "dur": 424, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449223236, "dur": 93, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449223333, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449223336, "dur": 66, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449223404, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449223432, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449223434, "dur": 129, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449223566, "dur": 72, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449223641, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449223643, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449223670, "dur": 533, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449224207, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449224232, "dur": 270, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449224507, "dur": 174, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449224683, "dur": 494, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449225181, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449225183, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449225214, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449225240, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449225242, "dur": 402, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449225647, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449225649, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449225681, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449225683, "dur": 98, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449225835, "dur": 323, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449226163, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449226187, "dur": 523, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449226715, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449226717, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449226750, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449226752, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449226783, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449226785, "dur": 112, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449226901, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449226903, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449226933, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449226935, "dur": 309, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449227248, "dur": 74, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449227326, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449227329, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449227355, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449227396, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449227462, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449227493, "dur": 658, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449228156, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449228184, "dur": 543, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449228731, "dur": 74, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449228806, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449228837, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449228839, "dur": 577, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449229420, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449229422, "dur": 88, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449229513, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449229515, "dur": 27, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449229545, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449229548, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449229571, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449229627, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449229629, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449229654, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449229680, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449229682, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449229707, "dur": 75, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449229785, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449229787, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449229877, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449229912, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449229915, "dur": 29, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449229946, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449229973, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449229975, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449229998, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449230024, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449230025, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449230085, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449230114, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449230116, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449230138, "dur": 71, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449230213, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449230216, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449230244, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449230279, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449230281, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449230305, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449230331, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449230354, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449230379, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449230381, "dur": 111, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449230493, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449230495, "dur": 79, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449230577, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449230580, "dur": 26, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449230612, "dur": 70, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449230683, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449230717, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449230720, "dur": 28, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449230752, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449230755, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449230791, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449230793, "dur": 27, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449230824, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449230827, "dur": 103, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449230934, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449230937, "dur": 31, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449230971, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449230973, "dur": 123, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449231100, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449231103, "dur": 51, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449231157, "dur": 2, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449231161, "dur": 38, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449231203, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449231206, "dur": 31, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449231239, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449231241, "dur": 31, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449231275, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449231277, "dur": 26, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449231306, "dur": 32, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449231342, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449231345, "dur": 122, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449231468, "dur": 2, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449231472, "dur": 28, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449231504, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449231506, "dur": 116, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449231625, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449231668, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449231671, "dur": 28, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449231702, "dur": 32, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449231738, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449231740, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449231813, "dur": 42, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449231856, "dur": 84, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449231944, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449231947, "dur": 76, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449232027, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449232029, "dur": 207, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449232241, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449232244, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449232289, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449232291, "dur": 49, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449232344, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449232373, "dur": 149, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449232526, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449232529, "dur": 148708, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449381251, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449381257, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449381326, "dur": 38, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449381366, "dur": 351347, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449732727, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449732732, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449732766, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449732770, "dur": 216192, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449948975, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449948979, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449949014, "dur": 25, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449949041, "dur": 13902, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449962953, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449962957, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449963020, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449963025, "dur": 2741, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449965778, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449965783, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449965849, "dur": 28, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506449965879, "dur": 327774, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506450293661, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506450293664, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506450293696, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506450293699, "dur": 867, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506450294571, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506450294574, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506450294655, "dur": 24, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506450294681, "dur": 364, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506450295051, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506450295054, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506450295096, "dur": 2196, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 36960, "tid": 12884901888, "ts": 1754506450297300, "dur": 10277, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 36960, "tid": 131324, "ts": 1754506450329858, "dur": 2258, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 36960, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 36960, "tid": 8589934592, "ts": 1754506449069771, "dur": 163971, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 36960, "tid": 8589934592, "ts": 1754506449233745, "dur": 4, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 36960, "tid": 8589934592, "ts": 1754506449233751, "dur": 1407, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 36960, "tid": 131324, "ts": 1754506450332120, "dur": 14, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 36960, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 36960, "tid": 4294967296, "ts": 1754506448951936, "dur": 1357422, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 36960, "tid": 4294967296, "ts": 1754506448989065, "dur": 7795, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 36960, "tid": 4294967296, "ts": 1754506450309403, "dur": 7275, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 36960, "tid": 4294967296, "ts": 1754506450312668, "dur": 231, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 36960, "tid": 4294967296, "ts": 1754506450316744, "dur": 12, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 36960, "tid": 131324, "ts": 1754506450332135, "dur": 10, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1754506449011532, "dur": 1694, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754506449013237, "dur": 1045, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754506449014458, "dur": 128, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1754506449014587, "dur": 429, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754506449015856, "dur": 58919, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_82714E7E5E4D2D26.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754506449075565, "dur": 1987, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754506449078088, "dur": 118, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754506449078341, "dur": 109, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754506449078699, "dur": 121, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754506449080910, "dur": 122, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mirror.CodeGen.ref.dll_27EAADF7DD649062.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754506449081161, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Mirror.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754506449081714, "dur": 97, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Configurations.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754506449082056, "dur": 99, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754506449082159, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754506449083097, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754506449086992, "dur": 100, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754506449087120, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rendering.LightTransport.Editor.ref.dll_F96E2A343514CFAE.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754506449087734, "dur": 92, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754506449094906, "dur": 123, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5693374507049717478.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754506449101641, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6947401630772442630.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754506449015038, "dur": 87566, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754506449102626, "dur": 1192462, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754506450295089, "dur": 133, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754506450295400, "dur": 66, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754506450295492, "dur": 1551, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1754506449015300, "dur": 87335, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449102665, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449103178, "dur": 157, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_B08776C80E53BDE9.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754506449103337, "dur": 918, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_48419839367BA1E6.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754506449104256, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449104896, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449105389, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449105509, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449105580, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754506449105728, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754506449105850, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449106023, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449106094, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1754506449106550, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449107144, "dur": 924, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449108113, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449108267, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449108337, "dur": 656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449108993, "dur": 626, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449109619, "dur": 850, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449110470, "dur": 1082, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449111553, "dur": 717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449112547, "dur": 628, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Manipulators\\Utils\\EditModeReplaceUtils.cs"}}, {"pid": 12345, "tid": 1, "ts": 1754506449112270, "dur": 1145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449113415, "dur": 364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449113779, "dur": 411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449114190, "dur": 377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449114567, "dur": 522, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449115089, "dur": 541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449115631, "dur": 754, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449116385, "dur": 689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449117074, "dur": 991, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449118065, "dur": 697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449118762, "dur": 663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449119827, "dur": 2311, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.multiplayer.playmode@7f4b34d911b7\\Workflow\\Editor\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 1, "ts": 1754506449119425, "dur": 2983, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449122408, "dur": 679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449123088, "dur": 1000, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449124089, "dur": 662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449124751, "dur": 659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449125411, "dur": 631, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449126042, "dur": 628, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449126675, "dur": 718, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Mirror.CompilerSymbols.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754506449127394, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449127582, "dur": 1072, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Mirror.CompilerSymbols.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754506449128655, "dur": 421, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449129090, "dur": 1276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Mirror.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754506449130367, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449130483, "dur": 1146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mirror.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754506449131629, "dur": 844, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449132485, "dur": 1673, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 1, "ts": 1754506449134159, "dur": 586, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449134752, "dur": 713, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449135906, "dur": 65196, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 1, "ts": 1754506449206085, "dur": 3653, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754506449209739, "dur": 506, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449210253, "dur": 2362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754506449212616, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449212684, "dur": 2167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754506449214852, "dur": 285, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449215144, "dur": 2641, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754506449217787, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449217882, "dur": 2827, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754506449220710, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449220903, "dur": 2946, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754506449223850, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449223967, "dur": 3206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754506449227174, "dur": 631, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449227816, "dur": 3398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Playmode.VirtualProjects.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754506449231215, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449231303, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Playmode.VirtualProjects.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1754506449231378, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449231452, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449231713, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449232046, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449232145, "dur": 3495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449238964, "dur": 194, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 1, "ts": 1754506449239159, "dur": 1016, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 1, "ts": 1754506449240176, "dur": 57, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 1, "ts": 1754506449235642, "dur": 4596, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754506449240240, "dur": 1054799, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754506449015368, "dur": 87287, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754506449102662, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_5ECB2C4C110CA6E5.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754506449103201, "dur": 527, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_6926138A25866A37.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754506449103730, "dur": 945, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_BA880D3F3B911009.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754506449104727, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754506449104801, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mirror.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754506449105126, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754506449105240, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754506449105370, "dur": 713, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1754506449106562, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754506449106753, "dur": 812, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754506449108057, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5693374507049717478.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754506449108154, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754506449108242, "dur": 386, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754506449108629, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754506449108957, "dur": 323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754506449109280, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754506449109598, "dur": 320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754506449109918, "dur": 959, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754506449111281, "dur": 1904, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio@198cdf337d13\\Editor\\VisualStudioEditor.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754506449110877, "dur": 2467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754506449113345, "dur": 697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754506449114043, "dur": 665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754506449114709, "dur": 812, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754506449115522, "dur": 707, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754506449116229, "dur": 628, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754506449116857, "dur": 633, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754506449117662, "dur": 997, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.adaptiveperformance@ffde895a1c6a\\Runtime\\Scalers\\AdaptiveSorting.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754506449117491, "dur": 1658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754506449119150, "dur": 724, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754506449119874, "dur": 803, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754506449120677, "dur": 1389, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754506449122067, "dur": 746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754506449122813, "dur": 703, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754506449123517, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754506449124174, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754506449124870, "dur": 896, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754506449125766, "dur": 260, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754506449126026, "dur": 677, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754506449126704, "dur": 1350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754506449128055, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754506449128245, "dur": 860, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754506449129105, "dur": 333, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754506449129476, "dur": 385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754506449129862, "dur": 773, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754506449130650, "dur": 1694, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754506449132344, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754506449132537, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754506449132598, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Mirror.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754506449132857, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754506449133220, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754506449133317, "dur": 2571, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754506449135889, "dur": 319, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754506449136264, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754506449136504, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754506449136592, "dur": 1037, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754506449137630, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754506449137788, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754506449138006, "dur": 659, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754506449138666, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754506449138814, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754506449138997, "dur": 312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754506449139310, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754506449139389, "dur": 417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754506449139807, "dur": 66291, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754506449206101, "dur": 3411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Mirror.Transports.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754506449209514, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754506449209656, "dur": 3181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754506449212838, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754506449212950, "dur": 2951, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754506449215902, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754506449215992, "dur": 2898, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754506449218898, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754506449219007, "dur": 2847, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754506449221855, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754506449221963, "dur": 3355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mobile.AndroidLogcat.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754506449225320, "dur": 348, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754506449225676, "dur": 3367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.Profiler.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754506449229043, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754506449229166, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.Profiler.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754506449229234, "dur": 3370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Mirror.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754506449232661, "dur": 1062348, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754506449015677, "dur": 88048, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754506449103730, "dur": 822, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_7C8AF205DD25174E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754506449104552, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754506449104621, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_6717AAFEBC09DAE0.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754506449104773, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754506449104823, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_6717AAFEBC09DAE0.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754506449104876, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754506449105458, "dur": 706, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1754506449106310, "dur": 890, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754506449107206, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754506449107716, "dur": 544, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754506449108265, "dur": 713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754506449108979, "dur": 612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754506449109592, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754506449110274, "dur": 1121, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754506449111396, "dur": 664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754506449112576, "dur": 649, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Window\\TimelineWindow.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754506449112060, "dur": 1373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754506449113433, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754506449114130, "dur": 700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754506449114831, "dur": 665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754506449115873, "dur": 1526, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@39bda2df468a\\Runtime\\2D\\RendererFeatures\\ScriptableRenderPass2D.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754506449115497, "dur": 2306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754506449117804, "dur": 760, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754506449118565, "dur": 869, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754506449119435, "dur": 975, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754506449120411, "dur": 714, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754506449121125, "dur": 850, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754506449121975, "dur": 817, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754506449122792, "dur": 675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754506449123467, "dur": 767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754506449124235, "dur": 758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754506449124993, "dur": 669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754506449125662, "dur": 366, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754506449126028, "dur": 672, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754506449126702, "dur": 431, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754506449127134, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754506449127240, "dur": 2720, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754506449129961, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754506449130137, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754506449130414, "dur": 2103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754506449132519, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754506449132749, "dur": 277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754506449133028, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754506449133094, "dur": 2283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754506449135377, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754506449135608, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754506449135881, "dur": 809, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754506449136690, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754506449136851, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754506449137167, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754506449137235, "dur": 869, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754506449138105, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754506449138239, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754506449138441, "dur": 575, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754506449139018, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754506449139116, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754506449139215, "dur": 495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754506449139802, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754506449139962, "dur": 310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/com.unity.cinemachine.editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754506449140316, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754506449140550, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754506449140608, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754506449141724, "dur": 239837, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754506449386627, "dur": 342271, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754506449386566, "dur": 344027, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754506449732682, "dur": 345, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754506449733543, "dur": 215850, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754506449963232, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1754506449963217, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1754506449963365, "dur": 2788, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1754506449966161, "dur": 328859, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449015350, "dur": 87298, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449102664, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449102972, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_2A749ACEEBE3A48E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754506449103258, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_CF55AD57D7032CA8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754506449103334, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449103415, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_5B6051ACD428DB50.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754506449103499, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449103591, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_0E6B7AA98D9EB3C0.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754506449103888, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449104026, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_40911F3A652DB05F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754506449104232, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449104368, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449104448, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449104587, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754506449104717, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449104803, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449105599, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449106060, "dur": 423, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449106584, "dur": 517, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449107107, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449107334, "dur": 912, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754506449108275, "dur": 662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449108937, "dur": 590, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449109528, "dur": 597, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449110125, "dur": 742, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449110867, "dur": 648, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449111515, "dur": 690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449112584, "dur": 778, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Signals\\SignalEmitterInspector.cs"}}, {"pid": 12345, "tid": 4, "ts": 1754506449112205, "dur": 1440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449113646, "dur": 715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449114361, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449115032, "dur": 670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449115702, "dur": 822, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449116524, "dur": 930, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449117455, "dur": 624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449118079, "dur": 651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449118730, "dur": 497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449119227, "dur": 484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449119711, "dur": 763, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449120474, "dur": 655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449121130, "dur": 702, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449121832, "dur": 711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449122544, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449123241, "dur": 776, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449124018, "dur": 670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449124689, "dur": 632, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449125395, "dur": 638, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449126033, "dur": 685, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449126720, "dur": 509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754506449127230, "dur": 702, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449127942, "dur": 1122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754506449129065, "dur": 445, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449129524, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449129620, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Configurations.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754506449129956, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449130017, "dur": 1054, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Configurations.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754506449131072, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449131193, "dur": 323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754506449131566, "dur": 830, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754506449132396, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449132575, "dur": 333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rendering.LightTransport.Runtime.ref.dll_35E0707C8501A09D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754506449132908, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449133054, "dur": 849, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Mirror.Examples.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754506449133904, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449133963, "dur": 1392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Edgegap.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754506449135356, "dur": 442, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449135812, "dur": 678, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.022.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754506449136491, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449136768, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449136862, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Profiler.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754506449137152, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449137240, "dur": 556, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Profiler.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754506449137797, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449137970, "dur": 1879, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449139850, "dur": 66260, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449206112, "dur": 3598, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754506449209712, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449209830, "dur": 2561, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Playmode.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754506449212392, "dur": 1056, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449213453, "dur": 2557, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Mirror.Authenticators.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754506449216015, "dur": 545, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449216568, "dur": 2855, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754506449219424, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449219509, "dur": 2927, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754506449222437, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449222723, "dur": 3236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754506449225960, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449226090, "dur": 3332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754506449229423, "dur": 533, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449229982, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449230165, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449230358, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449230507, "dur": 346, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449230864, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449230937, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449230998, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449231180, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449231569, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449231639, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449231933, "dur": 198, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rendering.LightTransport.Runtime.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1754506449232133, "dur": 517, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449232658, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754506449232716, "dur": 1062379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449015516, "dur": 87674, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449103697, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_FC9FF88BB6DDE854.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754506449103806, "dur": 964, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_FC9FF88BB6DDE854.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754506449104773, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754506449105442, "dur": 262, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1754506449105706, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449105799, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754506449105896, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449106055, "dur": 309, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449106543, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449107278, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449107398, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7043721260763788226.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754506449108212, "dur": 1950, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17296387151066238333.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754506449110163, "dur": 701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449110865, "dur": 1081, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449112572, "dur": 640, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\InputSystem\\Plugins\\InputForUI\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 5, "ts": 1754506449111946, "dur": 1318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449113265, "dur": 702, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449113967, "dur": 652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449114619, "dur": 685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449115305, "dur": 873, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449116178, "dur": 699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449116878, "dur": 704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449117583, "dur": 712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449118296, "dur": 706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449119003, "dur": 736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449119739, "dur": 726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449120465, "dur": 742, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449121207, "dur": 678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449121885, "dur": 673, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449122558, "dur": 732, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449123290, "dur": 736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449124027, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449124763, "dur": 161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449124924, "dur": 703, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449125628, "dur": 394, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449126022, "dur": 717, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449126741, "dur": 1446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754506449128188, "dur": 295, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449128491, "dur": 879, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754506449129371, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449129625, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449129734, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Mirror.Transports.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754506449130058, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754506449130248, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754506449130303, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754506449130512, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449130578, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mobile.AndroidLogcat.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754506449130781, "dur": 1768, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754506449132549, "dur": 1107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449133673, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449133734, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754506449133989, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449134055, "dur": 721, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754506449134777, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449134956, "dur": 312, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449135278, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449135497, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.022.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754506449135768, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.WorkflowUI.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754506449136019, "dur": 629, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.WorkflowUI.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754506449136648, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449136770, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449136853, "dur": 312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754506449137166, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449137250, "dur": 618, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754506449137868, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449137959, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754506449138061, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754506449138400, "dur": 530, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449138955, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Extension.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754506449139058, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Extension.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754506449139386, "dur": 574, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449139960, "dur": 66131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449206093, "dur": 3536, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Mirror.Components.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754506449209630, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449209779, "dur": 3621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Telepathy.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754506449213401, "dur": 479, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449213889, "dur": 3197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.iOS.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754506449217087, "dur": 1029, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449218126, "dur": 2842, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754506449220970, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449221059, "dur": 3341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Playmode.Workflow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754506449224401, "dur": 543, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449224956, "dur": 3532, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.Simulator.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754506449228489, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449228600, "dur": 3541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754506449232142, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754506449232269, "dur": 1062737, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449015484, "dur": 87256, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449102769, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_B67EF2BB05ABACA5.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754506449103190, "dur": 357, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_82714E7E5E4D2D26.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754506449103562, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_2AD3F77C8CF90E8B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754506449103713, "dur": 282, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449104014, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_47932AF13437889B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754506449104341, "dur": 765, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1754506449105111, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449105381, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1754506449105718, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1754506449106086, "dur": 426, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1754506449106513, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Scenarios.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754506449106574, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449106814, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449106909, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449106984, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Edgegap.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754506449107041, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449108012, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10831531723267835799.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754506449108210, "dur": 1802, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7759033573704817190.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754506449110014, "dur": 859, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449110873, "dur": 810, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449111684, "dur": 821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449112587, "dur": 683, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\inspectors\\CurvesOwner\\CurvesOwnerInspectorHelper.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754506449112506, "dur": 1381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449113888, "dur": 646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449114534, "dur": 648, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449115183, "dur": 669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449115852, "dur": 709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449116561, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449117234, "dur": 704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449117939, "dur": 680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449118619, "dur": 729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449119348, "dur": 688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449120036, "dur": 707, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449120743, "dur": 684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449121427, "dur": 768, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449122196, "dur": 816, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449123012, "dur": 686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449123699, "dur": 661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449124360, "dur": 680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449125040, "dur": 459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449125499, "dur": 520, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449126020, "dur": 721, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449126743, "dur": 432, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754506449127176, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449127433, "dur": 946, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754506449128379, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449128581, "dur": 329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/SimpleWebTransport.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754506449128911, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449129011, "dur": 337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Telepathy.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754506449129349, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449129436, "dur": 1704, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Telepathy.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754506449131141, "dur": 471, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449131622, "dur": 759, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754506449132382, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449132528, "dur": 836, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PackageValidationSuite.Editor.Extension.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754506449133365, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449133484, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754506449133811, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754506449134083, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449134156, "dur": 1210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754506449135367, "dur": 1186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449136571, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449136730, "dur": 133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449136863, "dur": 976, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449137841, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754506449138018, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449138084, "dur": 488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754506449138572, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449138669, "dur": 1203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449139873, "dur": 66230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449206105, "dur": 3352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754506449209458, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449209617, "dur": 3548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754506449213172, "dur": 3007, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449216187, "dur": 2752, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PackageValidationSuite.Editor.Extension.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754506449218940, "dur": 3226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449222180, "dur": 3816, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754506449225997, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449226140, "dur": 3459, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/com.unity.cinemachine.editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754506449229599, "dur": 355, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449230010, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449230079, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Telepathy.dll"}}, {"pid": 12345, "tid": 6, "ts": 1754506449230156, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449230355, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449230478, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449230553, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449230997, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449231061, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449231214, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449231430, "dur": 209, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mobile.AndroidLogcat.Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1754506449231644, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449231750, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449231905, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449232096, "dur": 245, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449232346, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754506449232429, "dur": 1062535, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754506449015470, "dur": 87220, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754506449102706, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_08B2EE5ACE61D9A1.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754506449103206, "dur": 536, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_674D8A39E28088FE.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754506449103744, "dur": 936, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_C2658D8F62234B56.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754506449104680, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754506449104799, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754506449104973, "dur": 12035, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754506449117009, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754506449117207, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754506449117423, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754506449117486, "dur": 8370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754506449125858, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754506449126038, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754506449126120, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754506449126216, "dur": 328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754506449126545, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754506449126669, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754506449126772, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754506449126964, "dur": 786, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754506449127751, "dur": 267, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754506449128080, "dur": 418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754506449128522, "dur": 1882, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754506449130405, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754506449130606, "dur": 386, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754506449131016, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754506449131304, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754506449131515, "dur": 577, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754506449132092, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754506449132173, "dur": 1051, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Unified.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754506449133225, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754506449133451, "dur": 675, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/EncryptionTransportEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754506449134127, "dur": 523, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754506449134650, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/EncryptionTransportEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754506449134717, "dur": 1079, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754506449135797, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754506449135978, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754506449136233, "dur": 658, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754506449136892, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754506449137046, "dur": 2764, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754506449139811, "dur": 66284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754506449206098, "dur": 3561, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754506449209660, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754506449209782, "dur": 3235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754506449213018, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754506449213232, "dur": 3107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754506449216340, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754506449216442, "dur": 2817, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.PlayMode.Editor.Bridge.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754506449219260, "dur": 378, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754506449219647, "dur": 4052, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Playmode.WorkflowUI.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754506449223700, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754506449223780, "dur": 3348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754506449227129, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754506449227231, "dur": 3872, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PackageValidationSuite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754506449231104, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754506449231420, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1754506449231478, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754506449231537, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754506449231618, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754506449231782, "dur": 323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Searcher.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1754506449232106, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754506449232191, "dur": 731032, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754506449963301, "dur": 330754, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754506449963225, "dur": 330833, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1754506450294076, "dur": 821, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1754506449015590, "dur": 87641, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754506449103689, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_BC191A0A0D36605B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754506449103810, "dur": 916, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_BC191A0A0D36605B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754506449104828, "dur": 206, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1754506449105079, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1754506449105192, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754506449105674, "dur": 145, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1754506449105993, "dur": 162, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1754506449106172, "dur": 285, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1754506449106517, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754506449106585, "dur": 1658, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754506449108246, "dur": 348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754506449108594, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754506449108929, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754506449109232, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754506449109549, "dur": 320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754506449109869, "dur": 853, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754506449110723, "dur": 487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754506449111210, "dur": 514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754506449111724, "dur": 469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754506449112573, "dur": 613, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\TimelineHelpers.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754506449112193, "dur": 1201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754506449113394, "dur": 702, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754506449114097, "dur": 641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754506449114739, "dur": 833, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754506449115572, "dur": 757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754506449116329, "dur": 652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754506449116981, "dur": 1011, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754506449117992, "dur": 489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754506449118583, "dur": 1268, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Inspection\\MacroEditor.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754506449118482, "dur": 1742, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754506449120224, "dur": 483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754506449120707, "dur": 480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754506449121188, "dur": 483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754506449121671, "dur": 504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754506449122176, "dur": 525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754506449122702, "dur": 508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754506449123210, "dur": 537, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754506449123747, "dur": 496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754506449124244, "dur": 487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754506449124731, "dur": 531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754506449125318, "dur": 50, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754506449125405, "dur": 609, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754506449126014, "dur": 825, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754506449126840, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754506449127135, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754506449127193, "dur": 857, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754506449128051, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754506449128229, "dur": 406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754506449128661, "dur": 563, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754506449129224, "dur": 815, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754506449130094, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PackageValidationSuite.Editor.Extension.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754506449130422, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754506449130705, "dur": 708, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754506449131414, "dur": 985, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754506449132403, "dur": 1981, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754506449134385, "dur": 361, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754506449134840, "dur": 2143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Mirror.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754506449136984, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754506449137094, "dur": 2710, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754506449139804, "dur": 66281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754506449206101, "dur": 3360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754506449209462, "dur": 455, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754506449209925, "dur": 12966, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Profiling.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754506449222892, "dur": 2722, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754506449225621, "dur": 2155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754506449227777, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754506449227843, "dur": 2470, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754506449230313, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754506449230466, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754506449230596, "dur": 499, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754506449231103, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754506449231165, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754506449231252, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/EncryptionTransportEditor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1754506449231464, "dur": 455, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754506449231919, "dur": 216, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Playmode.Workflow.Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1754506449232136, "dur": 705, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754506449232841, "dur": 1062148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754506449015647, "dur": 88066, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754506449103718, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_AE2A3D5B7498DE25.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754506449103823, "dur": 950, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_AE2A3D5B7498DE25.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754506449104839, "dur": 260, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1754506449105100, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754506449105320, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754506449105645, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1754506449106142, "dur": 4016, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754506449110164, "dur": 410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754506449110574, "dur": 470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754506449111044, "dur": 519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754506449111563, "dur": 520, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754506449112294, "dur": 1047, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Utilities\\PropertyCollector.cs"}}, {"pid": 12345, "tid": 9, "ts": 1754506449112083, "dur": 1491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754506449113575, "dur": 498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754506449114073, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754506449114599, "dur": 801, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@5c8f96f445ce\\Editor\\Drawing\\Controls\\ButtonControl.cs"}}, {"pid": 12345, "tid": 9, "ts": 1754506449114528, "dur": 1251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754506449115779, "dur": 525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754506449116305, "dur": 459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754506449116764, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754506449117207, "dur": 599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754506449117806, "dur": 504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754506449118310, "dur": 528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754506449118838, "dur": 499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754506449119337, "dur": 512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754506449119849, "dur": 483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754506449120332, "dur": 503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754506449120835, "dur": 476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754506449121311, "dur": 484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754506449121796, "dur": 493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754506449122290, "dur": 530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754506449122821, "dur": 687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754506449123738, "dur": 1677, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@8017b507cc74\\Unity.Mathematics\\int4x4.gen.cs"}}, {"pid": 12345, "tid": 9, "ts": 1754506449123508, "dur": 2081, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754506449125589, "dur": 446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754506449126035, "dur": 640, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754506449126676, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Mirror.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754506449126831, "dur": 342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754506449127173, "dur": 298, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754506449127474, "dur": 453, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754506449127928, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754506449128040, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754506449128205, "dur": 1240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754506449129446, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754506449129736, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.VirtualProjects.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754506449130092, "dur": 677, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.VirtualProjects.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754506449130769, "dur": 1482, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754506449132260, "dur": 341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Playmode.VirtualProjects.Editor.ref.dll_6E5412C99E713961.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754506449132601, "dur": 654, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754506449133264, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.Workflow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754506449133452, "dur": 915, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.Workflow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754506449134368, "dur": 1207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754506449135594, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754506449135660, "dur": 1318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754506449137026, "dur": 600, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754506449137627, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754506449137735, "dur": 2065, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754506449139802, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754506449140010, "dur": 66077, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754506449206091, "dur": 4221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754506449210313, "dur": 1780, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754506449212103, "dur": 2968, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.Unified.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754506449215072, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754506449215186, "dur": 2638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Mirror.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754506449217825, "dur": 1149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754506449218983, "dur": 3238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754506449222222, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754506449222329, "dur": 4809, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.PlayMode.Scenarios.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754506449227139, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754506449227217, "dur": 3969, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754506449231192, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754506449231624, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754506449231919, "dur": 228, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AdaptivePerformance.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1754506449232192, "dur": 1062893, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754506449015610, "dur": 87631, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754506449103426, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_AC4D3319F12A168E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754506449103681, "dur": 681, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_3EC188A9A2685E71.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754506449104755, "dur": 463, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754506449105273, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754506449105440, "dur": 366, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754506449105853, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754506449106033, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754506449106578, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754506449108017, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6886376194011666012.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754506449108293, "dur": 647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754506449108940, "dur": 589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754506449109529, "dur": 590, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754506449110120, "dur": 655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754506449110775, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754506449111449, "dur": 647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754506449112586, "dur": 761, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\treeview\\TrackGui\\TimelineTrackErrorGUI.cs"}}, {"pid": 12345, "tid": 10, "ts": 1754506449112096, "dur": 1341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754506449113437, "dur": 483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754506449113920, "dur": 454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754506449114374, "dur": 472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754506449114846, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754506449115314, "dur": 474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754506449115788, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754506449116228, "dur": 345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754506449116573, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754506449117028, "dur": 771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754506449117799, "dur": 477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754506449118276, "dur": 524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754506449118800, "dur": 488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754506449119288, "dur": 463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754506449119751, "dur": 708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754506449120460, "dur": 675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754506449121135, "dur": 725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754506449122059, "dur": 592, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collections@56bff8827a7e\\Unity.Collections\\FixedStringFormat.gen.cs"}}, {"pid": 12345, "tid": 10, "ts": 1754506449121860, "dur": 1122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754506449122982, "dur": 558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754506449123715, "dur": 1191, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@8017b507cc74\\Unity.Mathematics\\double3x2.gen.cs"}}, {"pid": 12345, "tid": 10, "ts": 1754506449123541, "dur": 1630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754506449125395, "dur": 616, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754506449126051, "dur": 616, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754506449126668, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754506449126827, "dur": 750, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754506449127578, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754506449127811, "dur": 663, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754506449128475, "dur": 657, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754506449129135, "dur": 1011, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754506449130146, "dur": 858, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754506449131014, "dur": 295, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754506449131345, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754506449131516, "dur": 578, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754506449132095, "dur": 362, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754506449132461, "dur": 743, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754506449133205, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754506449133266, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Mirror.Authenticators.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754506449133423, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Edgegap.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754506449133545, "dur": 401, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754506449133952, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754506449134136, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754506449134360, "dur": 655, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754506449135016, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754506449135265, "dur": 893, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754506449136298, "dur": 606, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754506449136909, "dur": 2906, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754506449139815, "dur": 66267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754506449206091, "dur": 3274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754506449209366, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754506449209506, "dur": 3581, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754506449213089, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754506449213192, "dur": 7499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754506449220692, "dur": 877, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754506449221579, "dur": 2213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Edgegap.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754506449223793, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754506449223864, "dur": 2808, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754506449226672, "dur": 677, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754506449227355, "dur": 4922, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754506449232364, "dur": 1062713, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754506449015629, "dur": 87875, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754506449103514, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_18A3D624D5A4F4D6.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754506449103668, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754506449103788, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_7CCD9826AD528688.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754506449103863, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_22EE298EC1BAB2B0.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754506449104129, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_38DF6D39894AFB51.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754506449104232, "dur": 690, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_BFEF6BF3A445E475.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754506449104922, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754506449105047, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754506449105262, "dur": 9840, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754506449115103, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754506449115240, "dur": 554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754506449115795, "dur": 765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754506449116560, "dur": 810, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754506449117371, "dur": 685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754506449118057, "dur": 665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754506449118723, "dur": 474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754506449119197, "dur": 466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754506449119663, "dur": 517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754506449120180, "dur": 491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754506449120672, "dur": 493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754506449121166, "dur": 502, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754506449121668, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754506449122213, "dur": 474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754506449122687, "dur": 515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754506449123202, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754506449123937, "dur": 483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754506449124421, "dur": 582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754506449125003, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754506449125550, "dur": 470, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754506449126021, "dur": 716, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754506449126739, "dur": 1310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754506449128081, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754506449128319, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754506449128456, "dur": 952, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754506449129409, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754506449129622, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754506449129715, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754506449129967, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Mirror.Components.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754506449130133, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754506449130401, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Unified.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754506449130635, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754506449130860, "dur": 896, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754506449131757, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754506449131865, "dur": 1117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mobile.AndroidLogcat.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754506449132983, "dur": 564, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754506449133557, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754506449133673, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754506449133757, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754506449133978, "dur": 1127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754506449135106, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754506449135313, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754506449135592, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754506449135724, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Scenarios.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754506449135968, "dur": 515, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Scenarios.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754506449136483, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754506449136625, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754506449136791, "dur": 74, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754506449136865, "dur": 2315, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754506449139181, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754506449139420, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754506449139759, "dur": 68, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754506449139827, "dur": 66252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754506449206081, "dur": 2884, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754506449208966, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754506449209251, "dur": 3065, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SimpleWebTransport.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754506449212317, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754506449212436, "dur": 2965, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Playmode.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754506449215409, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754506449215534, "dur": 9040, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754506449224575, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754506449224653, "dur": 2603, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754506449227257, "dur": 1354, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754506449228618, "dur": 3658, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.Simulator.Extension.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754506449232276, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754506449232366, "dur": 1062626, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449015704, "dur": 88029, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449103736, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_72AEB35BFEDD1B08.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754506449103848, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449104344, "dur": 699, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754506449105377, "dur": 668, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Playmode.VirtualProjects.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754506449106124, "dur": 3846, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449109975, "dur": 383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449110359, "dur": 810, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449111169, "dur": 497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449111666, "dur": 469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449112575, "dur": 608, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\treeview\\IPropertyKeyDataSource.cs"}}, {"pid": 12345, "tid": 12, "ts": 1754506449112135, "dur": 1048, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449113183, "dur": 488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449113671, "dur": 528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449114200, "dur": 479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449114679, "dur": 493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449115172, "dur": 493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449115665, "dur": 470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449116135, "dur": 345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449116480, "dur": 344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449116825, "dur": 343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449117233, "dur": 516, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449117750, "dur": 501, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449118579, "dur": 960, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Meta\\ProjectSettingMetadata.cs"}}, {"pid": 12345, "tid": 12, "ts": 1754506449118252, "dur": 1413, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449119665, "dur": 593, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449120258, "dur": 497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449120755, "dur": 528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449121283, "dur": 510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449121793, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449122279, "dur": 495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449122774, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449123322, "dur": 504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449123827, "dur": 509, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449124336, "dur": 472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449124859, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449125392, "dur": 621, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449126013, "dur": 828, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449126841, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Android.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754506449126937, "dur": 466, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449127425, "dur": 754, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Android.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754506449128179, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449128283, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754506449128461, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449128530, "dur": 870, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754506449129401, "dur": 678, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449130131, "dur": 990, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754506449131123, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449131187, "dur": 337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754506449131561, "dur": 1570, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754506449133132, "dur": 547, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449133727, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754506449134058, "dur": 1098, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754506449135157, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449135343, "dur": 589, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449135941, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754506449136161, "dur": 1134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754506449137296, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449137476, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754506449137694, "dur": 746, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449138445, "dur": 397, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754506449138842, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449138930, "dur": 938, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449139869, "dur": 66224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449206095, "dur": 2471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754506449208567, "dur": 1043, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449209620, "dur": 2900, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754506449212521, "dur": 901, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449213429, "dur": 2871, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754506449216301, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449216378, "dur": 2283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754506449218663, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449218764, "dur": 4550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.PlayMode.Configurations.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754506449223315, "dur": 765, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449224089, "dur": 5385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754506449229479, "dur": 455, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449229953, "dur": 309, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449230267, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449230409, "dur": 295, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449230874, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449230992, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449231197, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449231275, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449231421, "dur": 165, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 12, "ts": 1754506449232019, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449232086, "dur": 250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754506449232378, "dur": 1062656, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754506449015721, "dur": 88027, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754506449103752, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_9DC33C2F513A3B3C.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754506449103914, "dur": 665, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754506449104702, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754506449105378, "dur": 316, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754506449105726, "dur": 412, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/SimpleWebTransport.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1754506449106541, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754506449106892, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754506449107129, "dur": 225, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1754506449108235, "dur": 342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754506449108577, "dur": 330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754506449108907, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754506449109227, "dur": 339, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754506449109566, "dur": 351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754506449109917, "dur": 325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754506449110242, "dur": 516, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754506449111655, "dur": 669, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.47f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Diagnostics.Abstractions.dll"}}, {"pid": 12345, "tid": 13, "ts": 1754506449110758, "dur": 1567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754506449112585, "dur": 643, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Manipulators\\Move\\MoveItemModeMix.cs"}}, {"pid": 12345, "tid": 13, "ts": 1754506449113309, "dur": 1335, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Manipulators\\EditMode.cs"}}, {"pid": 12345, "tid": 13, "ts": 1754506449112325, "dur": 2424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754506449114749, "dur": 481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754506449115559, "dur": 1687, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@5c8f96f445ce\\Editor\\Data\\Graphs\\Matrix2MaterialSlot.cs"}}, {"pid": 12345, "tid": 13, "ts": 1754506449115240, "dur": 2397, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754506449117637, "dur": 628, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754506449118265, "dur": 488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754506449118753, "dur": 454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754506449119207, "dur": 778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754506449119985, "dur": 503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754506449120488, "dur": 598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754506449121087, "dur": 585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754506449121672, "dur": 497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754506449122614, "dur": 857, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.package-validation-suite@536239bd7458\\Editor\\ValidationSuite\\ValidationTests\\Standards\\US0112-PackageContainsMetafile.cs"}}, {"pid": 12345, "tid": 13, "ts": 1754506449122169, "dur": 1334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754506449123503, "dur": 464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754506449123968, "dur": 462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754506449124854, "dur": 552, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.burst@59eb6f11d242\\Runtime\\Intrinsics\\Arm\\NEON_ctor.cs"}}, {"pid": 12345, "tid": 13, "ts": 1754506449124431, "dur": 999, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754506449125430, "dur": 595, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754506449126025, "dur": 817, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754506449126843, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.iOS.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754506449127113, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754506449127177, "dur": 782, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.iOS.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754506449127960, "dur": 445, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754506449128416, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PackageValidationSuite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754506449128685, "dur": 1138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PackageValidationSuite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754506449129824, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754506449129928, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754506449130009, "dur": 1919, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754506449131928, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754506449132043, "dur": 1409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754506449133453, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754506449133592, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mobile.AndroidLogcat.Editor.ref.dll_5ADD071D6D2A40EE.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754506449133785, "dur": 1427, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754506449135339, "dur": 1649, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754506449136989, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754506449137045, "dur": 532, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754506449137578, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754506449137744, "dur": 2054, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754506449139800, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1754506449139955, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1754506449140263, "dur": 69340, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754506449209605, "dur": 3331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754506449212938, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754506449213078, "dur": 2654, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754506449215733, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754506449215828, "dur": 4509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754506449220338, "dur": 285, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754506449220631, "dur": 2951, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754506449223582, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754506449223686, "dur": 2784, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754506449226477, "dur": 1218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754506449227703, "dur": 3634, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Playmode.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1754506449231338, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754506449231574, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1754506449232044, "dur": 149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1754506449232193, "dur": 1062888, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754506449015757, "dur": 88008, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754506449103781, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_C87E051A0722C372.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754506449103978, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754506449104839, "dur": 190, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1754506449105372, "dur": 223, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Editor.Bridge.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1754506449105804, "dur": 308, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754506449106220, "dur": 4052, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754506449110276, "dur": 711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754506449110987, "dur": 499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754506449111486, "dur": 461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754506449111947, "dur": 456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754506449112578, "dur": 695, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Items\\ClipItem.cs"}}, {"pid": 12345, "tid": 14, "ts": 1754506449113307, "dur": 660, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\inspectors\\TimelineProjectSettings.cs"}}, {"pid": 12345, "tid": 14, "ts": 1754506449112404, "dur": 1725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754506449114129, "dur": 371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754506449114500, "dur": 391, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754506449114891, "dur": 454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754506449115345, "dur": 474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754506449115820, "dur": 503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754506449116323, "dur": 464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754506449116787, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754506449117634, "dur": 981, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.rendering.light-transport@307bc27a498f\\Runtime\\UnifiedRayTracing\\Hardware\\HardwareRayTracingBackend.cs"}}, {"pid": 12345, "tid": 14, "ts": 1754506449117231, "dur": 1461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754506449118692, "dur": 491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754506449119183, "dur": 462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754506449119645, "dur": 514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754506449120160, "dur": 542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754506449120702, "dur": 490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754506449121192, "dur": 460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754506449121652, "dur": 480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754506449122132, "dur": 463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754506449122595, "dur": 507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754506449123102, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754506449123567, "dur": 757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754506449124324, "dur": 581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754506449124906, "dur": 660, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754506449125566, "dur": 444, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754506449126054, "dur": 786, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754506449126844, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754506449126974, "dur": 337, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754506449127315, "dur": 890, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754506449128206, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754506449128293, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754506449128490, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754506449128730, "dur": 1323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754506449130054, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754506449130253, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1754506449130520, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754506449130577, "dur": 1205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Mirror.Components.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754506449131782, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754506449131941, "dur": 1150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754506449133091, "dur": 523, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754506449133705, "dur": 1156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Mirror.Authenticators.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754506449134862, "dur": 298, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754506449135165, "dur": 765, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Mirror.Examples.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754506449135931, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754506449136014, "dur": 797, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1754506449136812, "dur": 890, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754506449137709, "dur": 2104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754506449139813, "dur": 66276, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754506449206091, "dur": 2332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/kcp2k.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754506449208424, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754506449208569, "dur": 9099, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754506449217669, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754506449217751, "dur": 2732, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Mirror.CompilerSymbols.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754506449220483, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754506449220558, "dur": 6545, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754506449227155, "dur": 4931, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.UI.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1754506449232087, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754506449232184, "dur": 8059, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1754506449240244, "dur": 1054722, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754506449015739, "dur": 88015, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754506449103759, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_B2C9ADF926E837D8.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754506449104561, "dur": 306, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754506449105114, "dur": 279, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754506449105787, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754506449106113, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754506449106201, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754506449106278, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Mirror.Transports.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1754506449106353, "dur": 3937, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754506449110293, "dur": 803, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754506449111097, "dur": 501, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754506449111599, "dur": 462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754506449112061, "dur": 478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754506449112584, "dur": 696, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Audio\\AudioTrackInspector.cs"}}, {"pid": 12345, "tid": 15, "ts": 1754506449112539, "dur": 1068, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754506449113954, "dur": 946, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@39bda2df468a\\Editor\\2D\\ShapeEditor\\GUIFramework\\LayoutData.cs"}}, {"pid": 12345, "tid": 15, "ts": 1754506449113607, "dur": 1324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754506449114932, "dur": 486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754506449115876, "dur": 1265, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@39bda2df468a\\Runtime\\2D\\Shadows\\ShadowProvider\\EdgeDictionary.cs"}}, {"pid": 12345, "tid": 15, "ts": 1754506449115418, "dur": 1757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754506449117215, "dur": 592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754506449117808, "dur": 515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754506449118323, "dur": 531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754506449118854, "dur": 536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754506449119390, "dur": 534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754506449119924, "dur": 559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754506449120484, "dur": 483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754506449120968, "dur": 587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754506449121556, "dur": 510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754506449122103, "dur": 1318, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Runtime\\Playables\\ActivationControlPlayable.cs"}}, {"pid": 12345, "tid": 15, "ts": 1754506449122066, "dur": 1950, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754506449124016, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754506449124481, "dur": 521, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754506449125002, "dur": 583, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754506449125585, "dur": 488, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754506449126074, "dur": 770, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754506449126844, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754506449127113, "dur": 653, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754506449127766, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754506449128039, "dur": 337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.UI.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754506449128377, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754506449128532, "dur": 1307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.UI.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754506449129840, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754506449130048, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754506449130249, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754506449130464, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754506449130651, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754506449130828, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754506449130954, "dur": 1397, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754506449132351, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754506449132458, "dur": 2762, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754506449135221, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754506449135315, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Editor.ref.dll_EB1F4B6E56116D4A.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754506449135479, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754506449135645, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754506449135780, "dur": 393, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754506449136176, "dur": 544, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1754506449136721, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754506449136856, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.ref.dll_D91762B7076BE462.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754506449136911, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754506449136989, "dur": 2813, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754506449139803, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1754506449140019, "dur": 69573, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754506449209601, "dur": 9507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754506449219110, "dur": 592, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754506449219710, "dur": 3942, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754506449223653, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754506449223724, "dur": 6079, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754506449229804, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1754506449229869, "dur": 2888, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1754506449232829, "dur": 1062168, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449015800, "dur": 87987, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449104133, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_C203261CAF21A327.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754506449104524, "dur": 539, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mirror.CodeGen.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1754506449105183, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449105922, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449106401, "dur": 1015, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449107425, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5803490648119114145.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754506449107537, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449107621, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4691676790321305727.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754506449107674, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449107753, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5916180605834378387.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754506449107865, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449107972, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10292501669419677951.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1754506449108179, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449108282, "dur": 694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449108977, "dur": 609, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449109586, "dur": 607, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449110193, "dur": 864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449112574, "dur": 778, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@f3fac7af1578\\Editor\\TMP\\TMP_SettingsEditor.cs"}}, {"pid": 12345, "tid": 16, "ts": 1754506449111058, "dur": 2295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449113353, "dur": 725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449114079, "dur": 667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449114746, "dur": 1018, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449115879, "dur": 1514, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@39bda2df468a\\Runtime\\UniversalRenderPipelineCore.cs"}}, {"pid": 12345, "tid": 16, "ts": 1754506449115764, "dur": 2167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449117932, "dur": 688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449118620, "dur": 716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449119336, "dur": 700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449120037, "dur": 686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449120723, "dur": 669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449121393, "dur": 683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449122076, "dur": 677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449122754, "dur": 715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449123469, "dur": 701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449124171, "dur": 661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449124898, "dur": 731, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449125630, "dur": 387, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449126018, "dur": 731, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449126751, "dur": 327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Editor.Bridge.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754506449127079, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449127149, "dur": 940, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.PlayMode.Editor.Bridge.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754506449128090, "dur": 516, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449128660, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/kcp2k.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754506449128881, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449129022, "dur": 1195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SimpleWebTransport.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754506449130218, "dur": 300, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449130527, "dur": 1021, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/kcp2k.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754506449131549, "dur": 790, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449132345, "dur": 1363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Mirror.Transports.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754506449133709, "dur": 1146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449134864, "dur": 817, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/EncryptionTransportEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754506449135682, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449135803, "dur": 665, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754506449136469, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449136606, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449136693, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449136752, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449136862, "dur": 626, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449137489, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1754506449137713, "dur": 1268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1754506449138982, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449139054, "dur": 793, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449139847, "dur": 66228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449206078, "dur": 4014, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754506449210094, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449210302, "dur": 2914, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Mirror.Examples.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754506449213217, "dur": 391, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449213615, "dur": 2839, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.Android.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754506449216455, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449216565, "dur": 2884, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.022.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754506449219450, "dur": 392, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449219850, "dur": 3075, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754506449222926, "dur": 283, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449223221, "dur": 3296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/EncryptionTransportEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754506449226518, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449226609, "dur": 3367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1754506449229977, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449230105, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449230185, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449230304, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449230555, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449230714, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449231001, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449231253, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449231349, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449231905, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Playmode.Common.Runtime.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1754506449231960, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449232074, "dur": 161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449232239, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1754506449232302, "dur": 1062697, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754506450304763, "dur": 2637, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 36960, "tid": 131324, "ts": 1754506450333073, "dur": 68919, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 36960, "tid": 131324, "ts": 1754506450402080, "dur": 2353, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 36960, "tid": 131324, "ts": 1754506450327792, "dur": 77418, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}