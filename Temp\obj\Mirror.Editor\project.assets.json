{"version": 3, "targets": {".NETStandard,Version=v2.1": {"Mirror/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Mirror.CompilerSymbols": "1.0.0"}, "compile": {"bin/placeholder/Mirror.dll": {}}, "runtime": {"bin/placeholder/Mirror.dll": {}}}, "Mirror.CompilerSymbols/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/Mirror.CompilerSymbols.dll": {}}, "runtime": {"bin/placeholder/Mirror.CompilerSymbols.dll": {}}}, "Mirror.Components/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Mirror": "1.0.0"}, "compile": {"bin/placeholder/Mirror.Components.dll": {}}, "runtime": {"bin/placeholder/Mirror.Components.dll": {}}}, "Unity.Mirror.CodeGen/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Mirror": "1.0.0"}, "compile": {"bin/placeholder/Unity.Mirror.CodeGen.dll": {}}, "runtime": {"bin/placeholder/Unity.Mirror.CodeGen.dll": {}}}}}, "libraries": {"Mirror/1.0.0": {"type": "project", "path": "Mirror.csproj", "msbuildProject": "Mirror.csproj"}, "Mirror.CompilerSymbols/1.0.0": {"type": "project", "path": "Mirror.CompilerSymbols.csproj", "msbuildProject": "Mirror.CompilerSymbols.csproj"}, "Mirror.Components/1.0.0": {"type": "project", "path": "Mirror.Components.csproj", "msbuildProject": "Mirror.Components.csproj"}, "Unity.Mirror.CodeGen/1.0.0": {"type": "project", "path": "Unity.Mirror.CodeGen.csproj", "msbuildProject": "Unity.Mirror.CodeGen.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["Mirror >= 1.0.0", "Mirror.Components >= 1.0.0", "Unity.Mirror.CodeGen >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "c:\\Users\\<USER>\\ONU\\Mirror.Editor.csproj", "projectName": "Mirror.Editor", "projectPath": "c:\\Users\\<USER>\\ONU\\Mirror.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "c:\\Users\\<USER>\\ONU\\Temp\\obj\\Mirror.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"c:\\Users\\<USER>\\ONU\\Mirror.Components.csproj": {"projectPath": "c:\\Users\\<USER>\\ONU\\Mirror.Components.csproj"}, "c:\\Users\\<USER>\\ONU\\Mirror.csproj": {"projectPath": "c:\\Users\\<USER>\\ONU\\Mirror.csproj"}, "c:\\Users\\<USER>\\ONU\\Unity.Mirror.CodeGen.csproj": {"projectPath": "c:\\Users\\<USER>\\ONU\\Unity.Mirror.CodeGen.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.205\\RuntimeIdentifierGraph.json"}}}}